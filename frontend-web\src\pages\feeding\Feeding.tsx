import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { Box, Typography } from '@mui/material';
import { useAuth } from '../../contexts/AuthContext';
import FeedingDashboard from './FeedingDashboard';
import Inventory from './Inventory';
import FeedingPlans from './FeedingPlans';
import FeedingRecords from './FeedingRecords';
import Suppliers from './Suppliers';
import FeedingSchedules from './FeedingSchedules';
import FeedingNutrition from './FeedingNutrition';
import BetaFeedModule from '../../components/beta/BetaFeedModule';

const Feeding: React.FC = () => {
  const { user } = useAuth();

  // Check if user is BETA user
  const isBetaUser = user?.role === 'beta';

  // If BETA user, render BETA Feed Module
  if (isBetaUser) {
    return <BetaFeedModule />;
  }

  return (
    <Routes>
      <Route index element={<FeedingDashboard />} />
      <Route path="inventory" element={<Inventory />} />
      <Route path="plans" element={<FeedingPlans />} />
      <Route path="records" element={<FeedingRecords />} />
      <Route path="suppliers" element={<Suppliers />} />
      <Route path="schedules" element={<FeedingSchedules />} />
      <Route path="nutrition" element={<FeedingNutrition />} />
      <Route path="*" element={
        <Box p={4}>
          <Typography variant="h4">Feeding Management</Typography>
          <Typography variant="body1" mt={2}>
            Please select a specific feeding view from the navigation.
          </Typography>
        </Box>
      } />
    </Routes>
  );
};

export default Feeding;