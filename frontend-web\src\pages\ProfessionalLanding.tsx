import React, { useState } from 'react';
import { motion } from 'framer-motion';
import '../styles/professional-landing.css';

interface TabContent {
  id: string;
  label: string;
  content: React.ReactNode;
}

const ProfessionalLanding: React.FC = () => {
  const [activeTab, setActiveTab] = useState('home');

  const tabs: TabContent[] = [
    {
      id: 'home',
      label: 'Home',
      content: (
        <div className="tab-content">
          <div className="hero-section">
            <div className="hero-content">
              <motion.h1 
                className="hero-title"
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
              >
                🌾 AgriIntel
              </motion.h1>
              <motion.p 
                className="hero-subtitle"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
              >
                Smart Livestock Management for South African Farmers
              </motion.p>
              <motion.p 
                className="hero-description"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
              >
                Transform your farming operations with AI-powered insights, real-time monitoring, and predictive analytics. Join 15,000+ farmers who've revolutionized their livestock management.
              </motion.p>
              <motion.div
                className="hero-buttons"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
              >
                <a href="/register" className="cta-button register">
                  📝 Create Account
                </a>
                <a href="/beta-login" className="cta-button beta">
                  🚀 Start Free Beta
                </a>
                <a href="/premium-login" className="cta-button live">
                  ⚡ Go Premium
                </a>
              </motion.div>
            </div>
            <div className="hero-stats">
              <div className="stat-card">
                <div className="stat-number">15K+</div>
                <div className="stat-label">Active Farmers</div>
              </div>
              <div className="stat-card">
                <div className="stat-number">750K+</div>
                <div className="stat-label">Livestock Tracked</div>
              </div>
              <div className="stat-card">
                <div className="stat-number">99.9%</div>
                <div className="stat-label">System Uptime</div>
              </div>
              <div className="stat-card">
                <div className="stat-number">40%</div>
                <div className="stat-label">Cost Reduction</div>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'about',
      label: 'About Us',
      content: (
        <div className="tab-content">
          <div className="about-section">
            <h2>About AgriIntel</h2>
            <div className="about-grid">
              <div className="about-card">
                <div className="about-icon">🎯</div>
                <h3>Our Mission</h3>
                <p>To empower South African farmers with cutting-edge technology that transforms traditional livestock management into intelligent, data-driven operations.</p>
              </div>
              <div className="about-card">
                <div className="about-icon">🌍</div>
                <h3>Our Vision</h3>
                <p>Creating a sustainable future for agriculture in South Africa through innovative AI solutions and comprehensive farm management systems.</p>
              </div>
              <div className="about-card">
                <div className="about-icon">💡</div>
                <h3>Our Innovation</h3>
                <p>Combining IoT sensors, AI analytics, and mobile technology to provide real-time insights and predictive capabilities for modern farming.</p>
              </div>
            </div>
            <div className="company-stats">
              <h3>Why Choose AgriIntel?</h3>
              <div className="stats-grid">
                <div className="company-stat">
                  <strong>5+ Years</strong>
                  <span>Industry Experience</span>
                </div>
                <div className="company-stat">
                  <strong>24/7</strong>
                  <span>Technical Support</span>
                </div>
                <div className="company-stat">
                  <strong>ISO 27001</strong>
                  <span>Security Certified</span>
                </div>
                <div className="company-stat">
                  <strong>99.9%</strong>
                  <span>Customer Satisfaction</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'services',
      label: 'Services',
      content: (
        <div className="tab-content">
          <div className="services-section">
            <h2>Our Services</h2>
            <div className="services-grid">
              <div className="service-card">
                <div className="service-icon">🐄</div>
                <h3>Livestock Tracking</h3>
                <p>Real-time monitoring of cattle, sheep, and goats with RFID technology and GPS tracking.</p>
                <ul>
                  <li>Individual animal identification</li>
                  <li>Location tracking</li>
                  <li>Movement patterns analysis</li>
                  <li>Automated alerts</li>
                </ul>
              </div>
              <div className="service-card">
                <div className="service-icon">🩺</div>
                <h3>Health Management</h3>
                <p>AI-powered health monitoring and predictive analytics to prevent diseases and optimize care.</p>
                <ul>
                  <li>Health status monitoring</li>
                  <li>Disease prediction</li>
                  <li>Vaccination scheduling</li>
                  <li>Veterinary integration</li>
                </ul>
              </div>
              <div className="service-card">
                <div className="service-icon">🌱</div>
                <h3>Breeding Management</h3>
                <p>Comprehensive breeding program management with genetic tracking and optimization.</p>
                <ul>
                  <li>Breeding cycle tracking</li>
                  <li>Genetic analysis</li>
                  <li>Pregnancy monitoring</li>
                  <li>Birth predictions</li>
                </ul>
              </div>
              <div className="service-card">
                <div className="service-icon">📊</div>
                <h3>Analytics & Reporting</h3>
                <p>Advanced analytics and comprehensive reporting for data-driven decision making.</p>
                <ul>
                  <li>Performance analytics</li>
                  <li>Financial reporting</li>
                  <li>Trend analysis</li>
                  <li>Custom dashboards</li>
                </ul>
              </div>
              <div className="service-card">
                <div className="service-icon">🌾</div>
                <h3>Feed Management</h3>
                <p>Optimize feeding schedules and nutrition management for maximum efficiency.</p>
                <ul>
                  <li>Feed scheduling</li>
                  <li>Nutrition optimization</li>
                  <li>Cost tracking</li>
                  <li>Inventory management</li>
                </ul>
              </div>
              <div className="service-card">
                <div className="service-icon">💰</div>
                <h3>Financial Management</h3>
                <p>Complete financial tracking and management tools for your farming operations.</p>
                <ul>
                  <li>Expense tracking</li>
                  <li>Revenue analysis</li>
                  <li>Profit optimization</li>
                  <li>Budget planning</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'pricing',
      label: 'Pricing',
      content: (
        <div className="tab-content">
          <div className="pricing-section">
            <h2>Choose Your Plan</h2>
            <p className="pricing-subtitle">Select the perfect plan for your farming operation</p>
            <div className="pricing-grid">
              <div className="pricing-card beta">
                <div className="pricing-header">
                  <h3>Beta Access</h3>
                  <div className="price">FREE</div>
                  <p>Perfect for small-scale farmers</p>
                </div>
                <div className="pricing-features">
                  <ul>
                    <li>✅ Up to 50 animals</li>
                    <li>✅ Basic health monitoring</li>
                    <li>✅ Mobile app access</li>
                    <li>✅ Email support</li>
                    <li>✅ Basic reporting</li>
                  </ul>
                </div>
                <a href="/beta-login" className="pricing-button beta">
                  Start Free Beta
                </a>
              </div>
              
              <div className="pricing-card professional featured">
                <div className="pricing-badge">Most Popular</div>
                <div className="pricing-header">
                  <h3>Professional</h3>
                  <div className="price">R299<span>/month</span></div>
                  <p>For growing commercial farms</p>
                </div>
                <div className="pricing-features">
                  <ul>
                    <li>✅ Up to 500 animals</li>
                    <li>✅ AI health analytics</li>
                    <li>✅ Financial management</li>
                    <li>✅ Priority support</li>
                    <li>✅ Advanced reporting</li>
                    <li>✅ Breeding management</li>
                    <li>✅ Feed optimization</li>
                  </ul>
                </div>
                <a href="/premium-login" className="pricing-button professional">
                  Go Professional
                </a>
              </div>

              <div className="pricing-card enterprise">
                <div className="pricing-header">
                  <h3>Enterprise Pro</h3>
                  <div className="price">R599<span>/month</span></div>
                  <p>For large-scale operations</p>
                </div>
                <div className="pricing-features">
                  <ul>
                    <li>✅ Unlimited animals</li>
                    <li>✅ AI automation & agents</li>
                    <li>✅ Predictive analytics</li>
                    <li>✅ One-click task automation</li>
                    <li>✅ Auto feed ordering</li>
                    <li>✅ Auto vet appointments</li>
                    <li>✅ Custom integrations</li>
                    <li>✅ Dedicated support</li>
                  </ul>
                </div>
                <a href="/pro-login" className="pricing-button enterprise">
                  Go Enterprise Pro
                </a>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'contact',
      label: 'Contact',
      content: (
        <div className="tab-content">
          <div className="contact-section">
            <h2>Get In Touch</h2>
            <div className="contact-grid">
              <div className="contact-info">
                <h3>Contact Information</h3>
                <div className="contact-item">
                  <div className="contact-icon">📞</div>
                  <div>
                    <strong>Phone</strong>
                    <p>+27 11 123 4567</p>
                  </div>
                </div>
                <div className="contact-item">
                  <div className="contact-icon">📧</div>
                  <div>
                    <strong>Email</strong>
                    <p><EMAIL></p>
                  </div>
                </div>
                <div className="contact-item">
                  <div className="contact-icon">📍</div>
                  <div>
                    <strong>Address</strong>
                    <p>Cape Town, South Africa</p>
                  </div>
                </div>
                <div className="contact-item">
                  <div className="contact-icon">🕒</div>
                  <div>
                    <strong>Business Hours</strong>
                    <p>Mon-Fri: 8:00 AM - 6:00 PM</p>
                    <p>Sat: 9:00 AM - 2:00 PM</p>
                  </div>
                </div>
              </div>
              <div className="contact-form">
                <h3>Send us a Message</h3>
                <form>
                  <div className="form-group">
                    <input type="text" placeholder="Your Name" required />
                  </div>
                  <div className="form-group">
                    <input type="email" placeholder="Your Email" required />
                  </div>
                  <div className="form-group">
                    <input type="tel" placeholder="Your Phone" />
                  </div>
                  <div className="form-group">
                    <label htmlFor="service-interest" className="sr-only">Service Interest</label>
                    <select
                      id="service-interest"
                      name="serviceInterest"
                      required
                      aria-label="Select your service interest"
                      title="Choose the service you're interested in"
                    >
                      <option value="">Select Service Interest</option>
                      <option value="beta">Beta Access</option>
                      <option value="professional">Professional Plan</option>
                      <option value="enterprise">Enterprise Pro</option>
                      <option value="demo">Request Demo</option>
                    </select>
                  </div>
                  <div className="form-group">
                    <textarea placeholder="Your Message" rows={5} required></textarea>
                  </div>
                  <button type="submit" className="contact-submit">
                    Send Message
                  </button>
                </form>
              </div>
            </div>
          </div>
        </div>
      )
    }
  ];

  return (
    <div className="professional-landing">
      <nav className="landing-nav">
        <div className="nav-brand">
          <h1>🌾 AgriIntel</h1>
          <span className="brand-tagline">Smart Livestock Management</span>
        </div>
        <div className="nav-tabs">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              className={`nav-tab ${activeTab === tab.id ? 'active' : ''}`}
              onClick={() => setActiveTab(tab.id)}
            >
              {tab.label}
            </button>
          ))}
        </div>
        <div className="nav-actions">
          <a href="/register" className="nav-button register">CREATE ACCOUNT</a>
          <a href="/beta-login" className="nav-button beta">BETA</a>
          <a href="/premium-login" className="nav-button live">PREMIUM</a>
          <a href="/pro-login" className="nav-button pro">PRO</a>
        </div>
      </nav>

      <main className="landing-main">
        {tabs.find(tab => tab.id === activeTab)?.content}
      </main>

      <footer className="landing-footer">
        <div className="footer-content">
          <div className="footer-section">
            <h3>🌾 AgriIntel</h3>
            <p>Smart Livestock Management for South African Farmers</p>
            <div className="footer-social">
              <span>Follow us:</span>
              <a href="#">Facebook</a>
              <a href="#">Twitter</a>
              <a href="#">LinkedIn</a>
            </div>
          </div>
          <div className="footer-section">
            <h4>Quick Links</h4>
            <ul>
              <li><button onClick={() => setActiveTab('home')}>Home</button></li>
              <li><button onClick={() => setActiveTab('about')}>About</button></li>
              <li><button onClick={() => setActiveTab('services')}>Services</button></li>
              <li><button onClick={() => setActiveTab('pricing')}>Pricing</button></li>
            </ul>
          </div>
          <div className="footer-section">
            <h4>Contact</h4>
            <p>📞 +27 11 123 4567</p>
            <p>📧 <EMAIL></p>
            <p>📍 Cape Town, South Africa</p>
          </div>
        </div>
        <div className="footer-bottom">
          <p>© 2024 AgriIntel. All rights reserved. Made with ❤️ for South African farmers.</p>
        </div>
      </footer>
    </div>
  );
};

export default ProfessionalLanding;
