import React from 'react';
import '../styles/landing-components.css';
import '../styles/simple-landing.css';

const SimpleLanding: React.FC = () => {
  const handleBetaClick = () => {
    window.location.href = '/beta-login';
  };

  const handleLiveClick = () => {
    window.location.href = '/login';
  };

  return (
    <div className="basic-landing-container">
      {/* Header */}
      <header className="basic-landing-header">
        <h1 className="basic-landing-title">🌾 AgriIntel</h1>
        <div>
          <button
            onClick={handleBetaClick}
            className="landing-button beta"
          >
            BETA ACCESS
          </button>
          <button
            onClick={handleLiveClick}
            className="landing-button live"
          >
            LIVE ACCESS
          </button>
        </div>
      </header>

      {/* Main Content */}
      <main className="basic-landing-main">
        <h2 className="basic-landing-hero-title">
          Smart Livestock Management for South Africa
        </h2>

        <p className="basic-landing-hero-subtitle">
          Join 15,000+ farmers who've transformed their operations with AI technology
        </p>

        {/* Action Buttons */}
        <div className="basic-landing-action-buttons">
          <button
            onClick={handleBetaClick}
            className="landing-cta-button-large beta"
          >
            🚀 START FREE BETA
          </button>
          <button
            onClick={handleLiveClick}
            className="landing-cta-button-large live-outline"
          >
            ⚡ GO LIVE
          </button>
        </div>

        {/* Features Grid */}
        <div className="simple-landing-features-grid">
          <div className="simple-landing-feature-card">
            <div className="simple-landing-feature-icon">🐄</div>
            <h3>Smart Livestock Tracking</h3>
            <p>Real-time monitoring of cattle, sheep, and goats with RFID technology</p>
          </div>

          <div className="simple-landing-feature-card">
            <div className="simple-landing-feature-icon">🩺</div>
            <h3>AI Health Management</h3>
            <p>Predictive health analytics to prevent diseases and reduce costs</p>
          </div>

          <div className="simple-landing-feature-card">
            <div className="simple-landing-feature-icon">📊</div>
            <h3>Advanced Analytics</h3>
            <p>Comprehensive insights and reporting for better farm management</p>
          </div>
        </div>

        {/* Stats */}
        <div className="simple-landing-stats-grid">
          <div className="simple-landing-stat-item">
            <div className="simple-landing-stat-number">15K+</div>
            <div>South African Farmers</div>
          </div>
          <div className="simple-landing-stat-item">
            <div className="simple-landing-stat-number">750K+</div>
            <div>Livestock Tracked</div>
          </div>
          <div className="simple-landing-stat-item">
            <div className="simple-landing-stat-number">99.9%</div>
            <div>System Uptime</div>
          </div>
          <div className="simple-landing-stat-item">
            <div className="simple-landing-stat-number">40%</div>
            <div>Cost Reduction</div>
          </div>
        </div>

        {/* Pricing */}
        <div className="simple-landing-pricing-grid">
          <div className="simple-landing-pricing-card">
            <h3>Beta Access</h3>
            <div className="simple-landing-pricing-price">FREE</div>
            <p>Perfect for small-scale farmers</p>
            <ul className="simple-landing-pricing-features">
              <li>✅ Up to 50 animals</li>
              <li>✅ Basic health monitoring</li>
              <li>✅ Mobile app access</li>
            </ul>
            <button
              onClick={handleBetaClick}
              className="simple-landing-pricing-button beta"
            >
              START BETA
            </button>
          </div>

          <div className="simple-landing-pricing-card featured">
            <h3>Professional</h3>
            <div className="simple-landing-pricing-price">R299</div>
            <p>For growing commercial farms</p>
            <ul className="simple-landing-pricing-features">
              <li>✅ Up to 500 animals</li>
              <li>✅ AI health analytics</li>
              <li>✅ Financial management</li>
              <li>✅ Priority support</li>
            </ul>
            <button
              onClick={handleLiveClick}
              className="simple-landing-pricing-button live"
            >
              GO LIVE
            </button>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="simple-landing-footer">
        <h3>🌾 AgriIntel</h3>
        <p>Smart Livestock Management for South African Farmers</p>
        <div className="simple-landing-footer-contact">
          <span>📞 +27 11 123 4567</span>
          <span>📧 <EMAIL></span>
          <span>📍 Cape Town, South Africa</span>
        </div>
        <p className="simple-landing-footer-copyright">
          © 2024 AgriIntel. All rights reserved. Made with ❤️ for South African farmers.
        </p>
      </footer>
    </div>
  );
};

export default SimpleLanding;
