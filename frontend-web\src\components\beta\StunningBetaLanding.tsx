import React, { useState } from 'react';
import {
  <PERSON>,
  Container,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Button,
  Chip,
  useTheme,
  alpha,
  IconButton,
  Avatar,
} from '@mui/material';
import {
  Dashboard,
  Pets,
  LocalHospital,
  Favorite,
  Restaurant,
  AccountBalance,
  Inventory,
  Store,
  Assessment,
  MenuBook,
  Settings,
  Gavel,
  Lock,
  Star,
  TrendingUp,
  Agriculture,
  Language,
  Logout,
  Palette,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { gradients, colors } from '../../theme/modernTheme';
import AnimatedBackground from '../common/AnimatedBackground';

interface StunningBetaLandingProps {
  userRole: string;
  userName: string;
  onModuleClick: (moduleId: string) => void;
  onLanguageChange: () => void;
  onThemeChange: () => void;
  onLogout: () => void;
}

interface ModuleCard {
  id: string;
  title: string;
  description: string;
  icon: React.ElementType;
  gradient: string;
  color: string;
  isPremium: boolean;
  features: string[];
  comingSoon?: boolean;
}

const StunningBetaLanding: React.FC<StunningBetaLandingProps> = ({
  userRole,
  userName,
  onModuleClick,
  onLanguageChange,
  onThemeChange,
  onLogout,
}) => {
  const theme = useTheme();
  const [hoveredCard, setHoveredCard] = useState<string | null>(null);

  const modules: ModuleCard[] = [
    {
      id: 'dashboard',
      title: 'Dashboard Management',
      description: 'Comprehensive overview of your farm operations with real-time analytics',
      icon: Dashboard,
      gradient: gradients.dashboard,
      color: colors.primary[500],
      isPremium: false,
      features: ['Real-time Analytics', 'Performance Metrics', 'Quick Actions', 'Status Overview'],
    },
    {
      id: 'animals',
      title: 'Animals Management',
      description: 'Track and manage your livestock with precision and care',
      icon: Pets,
      gradient: gradients.animals,
      color: '#FF6B6B',
      isPremium: false,
      features: ['Animal Profiles', 'Health Tracking', 'Breeding Records', 'Location Monitoring'],
    },
    {
      id: 'health',
      title: 'Health Management',
      description: 'Monitor and maintain the health of your livestock',
      icon: LocalHospital,
      gradient: gradients.health,
      color: '#4ECDC4',
      isPremium: false,
      features: ['Health Records', 'Vaccination Schedules', 'Treatment Plans', 'Vet Appointments'],
    },
    {
      id: 'breeding',
      title: 'Breeding Management',
      description: 'Optimize breeding programs for better genetics and productivity',
      icon: Favorite,
      gradient: gradients.breeding,
      color: '#FF8A80',
      isPremium: true,
      features: ['Breeding Calendar', 'Genetic Tracking', 'Pregnancy Monitoring', 'Offspring Records'],
    },
    {
      id: 'feeding',
      title: 'Feed Management',
      description: userRole === 'beta'
        ? 'Basic feed recording and cost tracking (BETA Limited)'
        : 'Manage nutrition and feeding schedules for optimal growth',
      icon: Restaurant,
      gradient: gradients.feeding,
      color: '#FFB74D',
      isPremium: false,
      features: userRole === 'beta'
        ? ['Basic Feed Records', 'Simple Cost Tracking', 'Pie Chart Reports', 'Admin-level Saving']
        : ['Feed Schedules', 'Nutrition Plans', 'Cost Tracking', 'Inventory Management'],
    },
    {
      id: 'financial',
      title: 'Financial Management',
      description: userRole === 'beta'
        ? 'Basic financial tracking with simple reports (BETA Limited)'
        : 'Track expenses, revenue, and profitability of your operations',
      icon: AccountBalance,
      gradient: gradients.financial,
      color: '#81C784',
      isPremium: false, // Now available in BETA with limitations
      features: userRole === 'beta'
        ? ['Basic Income Tracking', 'Simple Expense Records', 'Excel Reports Only', 'Limited Analytics']
        : ['Income Tracking', 'Expense Management', 'Profit Analysis', 'Financial Reports'],
    },
    {
      id: 'inventory',
      title: 'Inventory Management',
      description: 'Manage feed, supplies, and equipment inventory',
      icon: Inventory,
      gradient: gradients.inventory,
      color: '#9575CD',
      isPremium: true,
      features: ['Stock Levels', 'Purchase Orders', 'Supplier Management', 'Usage Tracking'],
    },
    {
      id: 'commercial',
      title: 'Commercial Management',
      description: 'Handle sales, marketing, and customer relationships',
      icon: Store,
      gradient: gradients.commercial,
      color: '#F06292',
      isPremium: true,
      features: ['Sales Tracking', 'Customer Management', 'Market Analysis', 'Order Processing'],
    },
    {
      id: 'reports',
      title: 'Reports Management',
      description: 'Generate comprehensive reports and analytics',
      icon: Assessment,
      gradient: gradients.reports,
      color: '#64B5F6',
      isPremium: false,
      features: ['Custom Reports', 'Data Visualization', 'Export Options', 'Scheduled Reports'],
    },
    {
      id: 'resources',
      title: 'Resources Management',
      description: 'Access farming resources, weather data, and market information',
      icon: MenuBook,
      gradient: gradients.resources,
      color: '#A1887F',
      isPremium: false,
      features: ['Weather Data', 'Market Prices', 'Farming Guides', 'Expert Advice'],
    },
    {
      id: 'settings',
      title: 'Settings Management',
      description: 'Configure system preferences and user settings',
      icon: Settings,
      gradient: gradients.settings,
      color: '#90A4AE',
      isPremium: false,
      features: ['User Preferences', 'System Configuration', 'Data Backup', 'Security Settings'],
    },
    {
      id: 'compliance',
      title: 'Compliance Management',
      description: 'Ensure regulatory compliance and maintain certifications',
      icon: Gavel,
      gradient: gradients.compliance,
      color: '#FFAB91',
      isPremium: true,
      features: ['Regulatory Tracking', 'Certification Management', 'Audit Trails', 'Documentation'],
    },
  ];

  const isLocked = (module: ModuleCard) => {
    return module.isPremium && userRole === 'beta';
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: gradients.pageBackground,
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* Animated Background */}
      <AnimatedBackground
        animationVariant="livestock"
        showFloatingElements={true}
        particleCount={25}
      />

      {/* Header */}
      <Box
        sx={{
          position: 'relative',
          zIndex: 10,
          background: `linear-gradient(135deg, 
            ${alpha(colors.primary[500], 0.95)} 0%, 
            ${alpha(colors.secondary[500], 0.9)} 100%
          )`,
          backdropFilter: 'blur(20px)',
          borderBottom: `1px solid ${alpha(colors.primary[300], 0.3)}`,
          py: 2,
        }}
      >
        <Container maxWidth="xl">
          <Box display="flex" justifyContent="space-between" alignItems="center">
            {/* Logo and Welcome */}
            <Box display="flex" alignItems="center" gap={3}>
              <Box display="flex" alignItems="center" gap={2}>
                <Avatar
                  sx={{
                    width: 48,
                    height: 48,
                    background: gradients.farm,
                    fontSize: 24,
                  }}
                >
                  <Agriculture />
                </Avatar>
                <Box>
                  <Typography
                    variant="h4"
                    sx={{
                      fontWeight: 800,
                      background: 'linear-gradient(45deg, #FFF 30%, #FFE082 90%)',
                      backgroundClip: 'text',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      textShadow: '0 2px 4px rgba(0,0,0,0.3)',
                    }}
                  >
                    AgriIntel
                  </Typography>
                  <Typography
                    variant="subtitle1"
                    sx={{ color: 'rgba(255,255,255,0.9)', fontWeight: 500 }}
                  >
                    Welcome back, {userName}
                  </Typography>
                </Box>
              </Box>

              {/* Plan Badge */}
              <Chip
                label={userRole === 'beta' ? 'BETA PLAN' : 'ENTERPRISE PLAN'}
                sx={{
                  background: userRole === 'beta' 
                    ? 'linear-gradient(45deg, #FF6B6B, #FF8E8E)'
                    : 'linear-gradient(45deg, #4CAF50, #66BB6A)',
                  color: 'white',
                  fontWeight: 700,
                  fontSize: '0.8rem',
                  height: 32,
                  '& .MuiChip-label': {
                    px: 2,
                  },
                }}
              />
            </Box>

            {/* Action Buttons */}
            <Box display="flex" alignItems="center" gap={1}>
              <IconButton
                onClick={onLanguageChange}
                sx={{
                  color: 'white',
                  background: alpha(colors.primary[600], 0.2),
                  '&:hover': {
                    background: alpha(colors.primary[600], 0.3),
                    transform: 'scale(1.05)',
                  },
                }}
              >
                <Language />
              </IconButton>
              
              <IconButton
                onClick={onThemeChange}
                sx={{
                  color: 'white',
                  background: alpha(colors.secondary[500], 0.2),
                  '&:hover': {
                    background: alpha(colors.secondary[500], 0.3),
                    transform: 'scale(1.05)',
                  },
                }}
              >
                <Palette />
              </IconButton>

              <Button
                variant="outlined"
                startIcon={<Logout />}
                onClick={onLogout}
                sx={{
                  color: 'white',
                  borderColor: 'rgba(255,255,255,0.3)',
                  '&:hover': {
                    borderColor: 'white',
                    background: alpha(colors.error[500], 0.1),
                  },
                }}
              >
                LOGOUT
              </Button>
            </Box>
          </Box>
        </Container>
      </Box>

      {/* Main Content */}
      <Container maxWidth="xl" sx={{ py: 4, position: 'relative', zIndex: 5 }}>
        {/* Title Section */}
        <Box textAlign="center" mb={6}>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <Typography
              variant="h2"
              sx={{
                fontWeight: 800,
                mb: 2,
                background: gradients.primary,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                textAlign: 'center',
              }}
            >
              {userRole === 'beta' ? 'Beta Access' : 'Enterprise Plan'}
            </Typography>
            <Typography
              variant="h5"
              sx={{
                color: theme.palette.text.secondary,
                fontWeight: 400,
                maxWidth: 600,
                mx: 'auto',
                lineHeight: 1.6,
              }}
            >
              {userRole === 'beta' 
                ? 'Explore our powerful livestock management features. Upgrade to unlock premium capabilities.'
                : 'Full access to all AgriIntel features for comprehensive farm management.'
              }
            </Typography>
          </motion.div>
        </Box>

        {/* Module Cards Grid */}
        <Grid container spacing={3}>
          {modules.map((module, index) => (
            <Grid item xs={12} sm={6} lg={4} key={module.id}>
              <motion.div
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ y: -8 }}
                onHoverStart={() => setHoveredCard(module.id)}
                onHoverEnd={() => setHoveredCard(null)}
              >
                <Card
                  onClick={() => !isLocked(module) && onModuleClick(module.id)}
                  sx={{
                    height: 320,
                    cursor: isLocked(module) ? 'not-allowed' : 'pointer',
                    borderRadius: 4,
                    background: gradients.cardBackground,
                    backdropFilter: 'blur(20px)',
                    border: `1px solid ${alpha(module.color, 0.2)}`,
                    position: 'relative',
                    overflow: 'hidden',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    opacity: isLocked(module) ? 0.7 : 1,
                    '&:hover': {
                      boxShadow: `0 20px 60px ${alpha(module.color, 0.25)}`,
                      '& .module-icon': {
                        transform: 'scale(1.1) rotate(5deg)',
                      },
                      '& .gradient-overlay': {
                        opacity: 0.15,
                      },
                    },
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      height: '4px',
                      background: module.gradient,
                      borderRadius: '16px 16px 0 0',
                    },
                  }}
                >
                  {/* Gradient Overlay */}
                  <Box
                    className="gradient-overlay"
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      background: module.gradient,
                      opacity: 0.05,
                      transition: 'opacity 0.3s ease',
                      borderRadius: 4,
                    }}
                  />

                  {/* Lock Overlay for Premium */}
                  {isLocked(module) && (
                    <Box
                      sx={{
                        position: 'absolute',
                        top: 16,
                        right: 16,
                        zIndex: 10,
                        background: alpha(colors.warning[500], 0.9),
                        borderRadius: '50%',
                        width: 40,
                        height: 40,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        boxShadow: '0 4px 12px rgba(0,0,0,0.2)',
                      }}
                    >
                      <Lock sx={{ color: 'white', fontSize: 20 }} />
                    </Box>
                  )}

                  <CardContent sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column' }}>
                    {/* Header */}
                    <Box display="flex" alignItems="center" gap={2} mb={2}>
                      <Box
                        className="module-icon"
                        sx={{
                          width: 56,
                          height: 56,
                          borderRadius: 3,
                          background: module.gradient,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          transition: 'all 0.3s ease',
                          boxShadow: `0 8px 32px ${alpha(module.color, 0.3)}`,
                        }}
                      >
                        <module.icon sx={{ fontSize: 28, color: 'white' }} />
                      </Box>
                      
                      <Box flex={1}>
                        <Typography
                          variant="h6"
                          sx={{
                            fontWeight: 700,
                            color: theme.palette.text.primary,
                            lineHeight: 1.3,
                            mb: 0.5,
                          }}
                        >
                          {module.title}
                        </Typography>
                        {module.isPremium && (
                          <Chip
                            label="PREMIUM"
                            size="small"
                            sx={{
                              height: 20,
                              fontSize: '0.6rem',
                              fontWeight: 700,
                              background: gradients.warning,
                              color: 'white',
                            }}
                          />
                        )}
                      </Box>
                    </Box>

                    {/* Description */}
                    <Typography
                      variant="body2"
                      sx={{
                        color: theme.palette.text.secondary,
                        lineHeight: 1.5,
                        mb: 2,
                        flex: 1,
                      }}
                    >
                      {module.description}
                    </Typography>

                    {/* Features */}
                    <Box>
                      <Typography
                        variant="caption"
                        sx={{
                          color: theme.palette.text.secondary,
                          fontWeight: 600,
                          textTransform: 'uppercase',
                          letterSpacing: '0.5px',
                          mb: 1,
                          display: 'block',
                        }}
                      >
                        Key Features
                      </Typography>
                      <Box display="flex" flexWrap="wrap" gap={0.5}>
                        {module.features.slice(0, 3).map((feature, idx) => (
                          <Chip
                            key={idx}
                            label={feature}
                            size="small"
                            sx={{
                              height: 24,
                              fontSize: '0.7rem',
                              backgroundColor: alpha(module.color, 0.1),
                              color: module.color,
                              border: `1px solid ${alpha(module.color, 0.2)}`,
                              '&:hover': {
                                backgroundColor: alpha(module.color, 0.15),
                              },
                            }}
                          />
                        ))}
                        {module.features.length > 3 && (
                          <Chip
                            label={`+${module.features.length - 3}`}
                            size="small"
                            sx={{
                              height: 24,
                              fontSize: '0.7rem',
                              backgroundColor: alpha(theme.palette.text.secondary, 0.1),
                              color: theme.palette.text.secondary,
                            }}
                          />
                        )}
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          ))}
        </Grid>

        {/* Upgrade Banner for Beta Users */}
        {userRole === 'beta' && (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
          >
            <Box
              sx={{
                mt: 6,
                p: 4,
                borderRadius: 4,
                background: gradients.sunset,
                color: 'white',
                textAlign: 'center',
                position: 'relative',
                overflow: 'hidden',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: -50,
                  right: -50,
                  width: 100,
                  height: 100,
                  borderRadius: '50%',
                  background: 'rgba(255, 255, 255, 0.1)',
                },
              }}
            >
              <Typography variant="h4" sx={{ fontWeight: 700, mb: 2 }}>
                🚀 Unlock Premium Features
              </Typography>
              <Typography variant="body1" sx={{ mb: 3, opacity: 0.9 }}>
                Upgrade to Enterprise Plan for full access to breeding management, financial tracking, 
                inventory control, and advanced analytics.
              </Typography>
              <Button
                variant="contained"
                size="large"
                startIcon={<Star />}
                sx={{
                  background: 'rgba(255, 255, 255, 0.2)',
                  backdropFilter: 'blur(10px)',
                  color: 'white',
                  fontWeight: 600,
                  px: 4,
                  py: 1.5,
                  '&:hover': {
                    background: 'rgba(255, 255, 255, 0.3)',
                    transform: 'translateY(-2px)',
                  },
                }}
              >
                UPGRADE NOW
              </Button>
            </Box>
          </motion.div>
        )}
      </Container>
    </Box>
  );
};

export default StunningBetaLanding;
