import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Avatar,
  Tabs,
  Tab,
  Badge,
} from '@mui/material';
import {
  Add,
  Download,
  Pets,
  Edit,
  Delete,
  Visibility,
  TrendingUp,
  Category,
  AutoAwesome,
  Upgrade,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import * as XLSX from 'xlsx';

interface Animal {
  id: string;
  tagNumber: string;
  name: string;
  species: string;
  breed: string;
  gender: 'male' | 'female';
  birthDate?: string;
  weight?: number;
  status: 'active' | 'sold' | 'deceased' | 'quarantined';
  healthStatus: 'healthy' | 'sick' | 'injured' | 'recovering';
  location: string;
  purchasePrice?: number;
  notes?: string;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`animals-tabpanel-${index}`}
      aria-labelledby={`animals-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const BetaAnimalsModule: React.FC = () => {
  const [animals, setAnimals] = useState<Animal[]>([]);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [selectedAnimal, setSelectedAnimal] = useState<Animal | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const [newAnimal, setNewAnimal] = useState<Partial<Animal>>({
    species: '',
    breed: '',
    gender: 'female',
    status: 'active',
    healthStatus: 'healthy',
    location: '',
  });

  // Sample data for BETA demonstration
  useEffect(() => {
    const sampleData: Animal[] = [
      {
        id: '1',
        tagNumber: 'C001',
        name: 'Bessie',
        species: 'Cattle',
        breed: 'Holstein',
        gender: 'female',
        birthDate: '2022-03-15',
        weight: 450,
        status: 'active',
        healthStatus: 'healthy',
        location: 'Pasture A',
        purchasePrice: 15000,
        notes: 'High milk producer'
      },
      {
        id: '2',
        tagNumber: 'C002',
        name: 'Daisy',
        species: 'Cattle',
        breed: 'Jersey',
        gender: 'female',
        birthDate: '2021-08-20',
        weight: 380,
        status: 'active',
        healthStatus: 'healthy',
        location: 'Pasture B',
        purchasePrice: 12000,
      },
      {
        id: '3',
        tagNumber: 'S001',
        name: 'Woolly',
        species: 'Sheep',
        breed: 'Merino',
        gender: 'male',
        birthDate: '2023-01-10',
        weight: 65,
        status: 'active',
        healthStatus: 'healthy',
        location: 'Paddock 1',
        purchasePrice: 2500,
      },
    ];
    setAnimals(sampleData);
  }, []);

  const handleAddAnimal = () => {
    if (animals.length >= 50) {
      alert('BETA Limitation: Maximum 50 animals allowed. Upgrade to Professional for unlimited animals.');
      return;
    }

    if (newAnimal.tagNumber && newAnimal.name && newAnimal.species && newAnimal.breed) {
      const animal: Animal = {
        id: Date.now().toString(),
        tagNumber: newAnimal.tagNumber,
        name: newAnimal.name,
        species: newAnimal.species,
        breed: newAnimal.breed,
        gender: newAnimal.gender || 'female',
        birthDate: newAnimal.birthDate,
        weight: newAnimal.weight,
        status: newAnimal.status || 'active',
        healthStatus: newAnimal.healthStatus || 'healthy',
        location: newAnimal.location || '',
        purchasePrice: newAnimal.purchasePrice,
        notes: newAnimal.notes,
      };
      setAnimals([...animals, animal]);
      setNewAnimal({
        species: '',
        breed: '',
        gender: 'female',
        status: 'active',
        healthStatus: 'healthy',
        location: '',
      });
      setShowAddDialog(false);
    }
  };

  const handleEditAnimal = (animal: Animal) => {
    setSelectedAnimal(animal);
    setNewAnimal(animal);
    setShowEditDialog(true);
  };

  const handleUpdateAnimal = () => {
    if (selectedAnimal && newAnimal.tagNumber && newAnimal.name) {
      const updatedAnimals = animals.map(animal =>
        animal.id === selectedAnimal.id ? { ...animal, ...newAnimal } : animal
      );
      setAnimals(updatedAnimals);
      setShowEditDialog(false);
      setSelectedAnimal(null);
      setNewAnimal({
        species: '',
        breed: '',
        gender: 'female',
        status: 'active',
        healthStatus: 'healthy',
        location: '',
      });
    }
  };

  const handleDeleteAnimal = (animalId: string) => {
    if (window.confirm('Are you sure you want to delete this animal?')) {
      setAnimals(animals.filter(animal => animal.id !== animalId));
    }
  };

  const exportToExcel = () => {
    const worksheet = XLSX.utils.json_to_sheet(animals);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Animals');
    XLSX.writeFile(workbook, 'AgriIntel_Animals_Report.xlsx');
  };

  // Category statistics
  const speciesStats = animals.reduce((acc, animal) => {
    acc[animal.species] = (acc[animal.species] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const healthStats = animals.reduce((acc, animal) => {
    acc[animal.healthStatus] = (acc[animal.healthStatus] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const statusStats = animals.reduce((acc, animal) => {
    acc[animal.status] = (acc[animal.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return (
    <Box sx={{ p: 3 }}>
      {/* BETA Limitation Alert */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>BETA Animals Module:</strong> Limited to 50 animals maximum with basic categorization. 
          Upgrade to Professional for unlimited animals, advanced search, and AI auto-categorization.
        </Typography>
      </Alert>

      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" fontWeight="bold">
          Animals Management (BETA)
        </Typography>
        <Box display="flex" gap={2}>
          <Badge badgeContent={animals.length} max={50} color="primary">
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => setShowAddDialog(true)}
              disabled={animals.length >= 50}
              sx={{ borderRadius: 2 }}
            >
              Add Animal
            </Button>
          </Badge>
          <Button
            variant="outlined"
            startIcon={<Download />}
            onClick={exportToExcel}
            sx={{ borderRadius: 2 }}
          >
            Export Excel
          </Button>
        </Box>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={3}>
          <motion.div whileHover={{ y: -4 }}>
            <Card sx={{ borderRadius: 3, background: 'linear-gradient(135deg, #4CAF50, #66BB6A)' }}>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" color="white" fontWeight="bold">
                      {animals.length}/50
                    </Typography>
                    <Typography variant="body2" color="rgba(255,255,255,0.8)">
                      Total Animals (BETA Limit)
                    </Typography>
                  </Box>
                  <Pets sx={{ fontSize: 40, color: 'rgba(255,255,255,0.8)' }} />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} md={3}>
          <motion.div whileHover={{ y: -4 }}>
            <Card sx={{ borderRadius: 3, background: 'linear-gradient(135deg, #2196F3, #42A5F5)' }}>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" color="white" fontWeight="bold">
                      {healthStats.healthy || 0}
                    </Typography>
                    <Typography variant="body2" color="rgba(255,255,255,0.8)">
                      Healthy Animals
                    </Typography>
                  </Box>
                  <TrendingUp sx={{ fontSize: 40, color: 'rgba(255,255,255,0.8)' }} />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} md={3}>
          <motion.div whileHover={{ y: -4 }}>
            <Card sx={{ borderRadius: 3, background: 'linear-gradient(135deg, #FF9800, #FFB74D)' }}>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" color="white" fontWeight="bold">
                      {Object.keys(speciesStats).length}
                    </Typography>
                    <Typography variant="body2" color="rgba(255,255,255,0.8)">
                      Species Types
                    </Typography>
                  </Box>
                  <Category sx={{ fontSize: 40, color: 'rgba(255,255,255,0.8)' }} />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} md={3}>
          <motion.div whileHover={{ y: -4 }}>
            <Card sx={{ borderRadius: 3, background: 'linear-gradient(135deg, #9C27B0, #BA68C8)' }}>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" color="white" fontWeight="bold">
                      {statusStats.active || 0}
                    </Typography>
                    <Typography variant="body2" color="rgba(255,255,255,0.8)">
                      Active Animals
                    </Typography>
                  </Box>
                  <Pets sx={{ fontSize: 40, color: 'rgba(255,255,255,0.8)' }} />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>
      </Grid>

      {/* Tabs */}
      <Card sx={{ borderRadius: 3 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
            <Tab label="Animals List" />
            <Tab label="Category Dashboard" />
            <Tab label="AI Features (Premium)" disabled />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          {/* Animals List */}
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Tag Number</TableCell>
                  <TableCell>Name</TableCell>
                  <TableCell>Species</TableCell>
                  <TableCell>Breed</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Health</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {animals.map((animal) => (
                  <TableRow key={animal.id}>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {animal.tagNumber}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box display="flex" alignItems="center" gap={1}>
                        <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
                          {animal.name.charAt(0)}
                        </Avatar>
                        {animal.name}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip label={animal.species} size="small" color="primary" />
                    </TableCell>
                    <TableCell>{animal.breed}</TableCell>
                    <TableCell>
                      <Chip
                        label={animal.status}
                        size="small"
                        color={animal.status === 'active' ? 'success' : 'default'}
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={animal.healthStatus}
                        size="small"
                        color={animal.healthStatus === 'healthy' ? 'success' : 'warning'}
                      />
                    </TableCell>
                    <TableCell>
                      <Box display="flex" gap={1}>
                        <IconButton size="small" onClick={() => handleEditAnimal(animal)}>
                          <Edit />
                        </IconButton>
                        <IconButton size="small" onClick={() => handleDeleteAnimal(animal.id)}>
                          <Delete />
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          {/* Category Dashboard */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Card sx={{ borderRadius: 2, height: '100%' }}>
                <CardContent>
                  <Typography variant="h6" fontWeight="bold" mb={2}>
                    Species Distribution
                  </Typography>
                  {Object.entries(speciesStats).map(([species, count]) => (
                    <Box key={species} mb={2}>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                        <Typography variant="body2">{species}</Typography>
                        <Typography variant="body2" fontWeight="bold">
                          {count} animals
                        </Typography>
                      </Box>
                      <Box
                        sx={{
                          height: 8,
                          backgroundColor: '#f0f0f0',
                          borderRadius: 4,
                          overflow: 'hidden'
                        }}
                      >
                        <Box
                          sx={{
                            height: '100%',
                            width: `${(count / animals.length) * 100}%`,
                            backgroundColor: '#4CAF50',
                            borderRadius: 4,
                          }}
                        />
                      </Box>
                    </Box>
                  ))}
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card sx={{ borderRadius: 2, height: '100%' }}>
                <CardContent>
                  <Typography variant="h6" fontWeight="bold" mb={2}>
                    Health Status
                  </Typography>
                  {Object.entries(healthStats).map(([status, count]) => (
                    <Box key={status} mb={2}>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                        <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
                          {status}
                        </Typography>
                        <Typography variant="body2" fontWeight="bold">
                          {count} animals
                        </Typography>
                      </Box>
                      <Box
                        sx={{
                          height: 8,
                          backgroundColor: '#f0f0f0',
                          borderRadius: 4,
                          overflow: 'hidden'
                        }}
                      >
                        <Box
                          sx={{
                            height: '100%',
                            width: `${(count / animals.length) * 100}%`,
                            backgroundColor: status === 'healthy' ? '#4CAF50' : '#FF9800',
                            borderRadius: 4,
                          }}
                        />
                      </Box>
                    </Box>
                  ))}
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card sx={{ borderRadius: 2, height: '100%' }}>
                <CardContent>
                  <Box display="flex" alignItems="center" justifyContent="between" mb={2}>
                    <Typography variant="h6" fontWeight="bold">
                      AI Auto-Categorization
                    </Typography>
                    <AutoAwesome sx={{ color: '#FF9800' }} />
                  </Box>
                  <Typography variant="body2" color="text.secondary" mb={2}>
                    Upgrade to Professional for:
                  </Typography>
                  <Box component="ul" sx={{ pl: 2, mb: 2 }}>
                    <Typography component="li" variant="body2" mb={1}>
                      Automatic breed identification
                    </Typography>
                    <Typography component="li" variant="body2" mb={1}>
                      Smart health predictions
                    </Typography>
                    <Typography component="li" variant="body2" mb={1}>
                      Performance categorization
                    </Typography>
                    <Typography component="li" variant="body2" mb={1}>
                      Breeding recommendations
                    </Typography>
                  </Box>
                  <Button
                    variant="contained"
                    fullWidth
                    startIcon={<Upgrade />}
                    sx={{
                      background: 'linear-gradient(135deg, #FF9800, #FFB74D)',
                      borderRadius: 2,
                    }}
                  >
                    Upgrade Now
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>
      </Card>

      {/* Add Animal Dialog */}
      <Dialog open={showAddDialog} onClose={() => setShowAddDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Add New Animal</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Tag Number"
                value={newAnimal.tagNumber || ''}
                onChange={(e) => setNewAnimal({ ...newAnimal, tagNumber: e.target.value })}
                placeholder="e.g., C001, S001"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Name"
                value={newAnimal.name || ''}
                onChange={(e) => setNewAnimal({ ...newAnimal, name: e.target.value })}
                placeholder="e.g., Bessie, Daisy"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Species</InputLabel>
                <Select
                  value={newAnimal.species || ''}
                  onChange={(e) => setNewAnimal({ ...newAnimal, species: e.target.value })}
                  label="Species"
                >
                  <MenuItem value="Cattle">Cattle</MenuItem>
                  <MenuItem value="Sheep">Sheep</MenuItem>
                  <MenuItem value="Goats">Goats</MenuItem>
                  <MenuItem value="Pigs">Pigs</MenuItem>
                  <MenuItem value="Chickens">Chickens</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Breed"
                value={newAnimal.breed || ''}
                onChange={(e) => setNewAnimal({ ...newAnimal, breed: e.target.value })}
                placeholder="e.g., Holstein, Jersey, Merino"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Gender</InputLabel>
                <Select
                  value={newAnimal.gender || 'female'}
                  onChange={(e) => setNewAnimal({ ...newAnimal, gender: e.target.value as 'male' | 'female' })}
                  label="Gender"
                >
                  <MenuItem value="male">Male</MenuItem>
                  <MenuItem value="female">Female</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Birth Date"
                type="date"
                value={newAnimal.birthDate || ''}
                onChange={(e) => setNewAnimal({ ...newAnimal, birthDate: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Weight (kg)"
                type="number"
                value={newAnimal.weight || ''}
                onChange={(e) => setNewAnimal({ ...newAnimal, weight: Number(e.target.value) })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Location"
                value={newAnimal.location || ''}
                onChange={(e) => setNewAnimal({ ...newAnimal, location: e.target.value })}
                placeholder="e.g., Pasture A, Paddock 1"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Notes (Optional)"
                value={newAnimal.notes || ''}
                onChange={(e) => setNewAnimal({ ...newAnimal, notes: e.target.value })}
                multiline
                rows={2}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowAddDialog(false)}>Cancel</Button>
          <Button variant="contained" onClick={handleAddAnimal}>
            Add Animal
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Animal Dialog */}
      <Dialog open={showEditDialog} onClose={() => setShowEditDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Edit Animal</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Tag Number"
                value={newAnimal.tagNumber || ''}
                onChange={(e) => setNewAnimal({ ...newAnimal, tagNumber: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Name"
                value={newAnimal.name || ''}
                onChange={(e) => setNewAnimal({ ...newAnimal, name: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Species</InputLabel>
                <Select
                  value={newAnimal.species || ''}
                  onChange={(e) => setNewAnimal({ ...newAnimal, species: e.target.value })}
                  label="Species"
                >
                  <MenuItem value="Cattle">Cattle</MenuItem>
                  <MenuItem value="Sheep">Sheep</MenuItem>
                  <MenuItem value="Goats">Goats</MenuItem>
                  <MenuItem value="Pigs">Pigs</MenuItem>
                  <MenuItem value="Chickens">Chickens</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Breed"
                value={newAnimal.breed || ''}
                onChange={(e) => setNewAnimal({ ...newAnimal, breed: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={newAnimal.status || 'active'}
                  onChange={(e) => setNewAnimal({ ...newAnimal, status: e.target.value as any })}
                  label="Status"
                >
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="sold">Sold</MenuItem>
                  <MenuItem value="deceased">Deceased</MenuItem>
                  <MenuItem value="quarantined">Quarantined</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Health Status</InputLabel>
                <Select
                  value={newAnimal.healthStatus || 'healthy'}
                  onChange={(e) => setNewAnimal({ ...newAnimal, healthStatus: e.target.value as any })}
                  label="Health Status"
                >
                  <MenuItem value="healthy">Healthy</MenuItem>
                  <MenuItem value="sick">Sick</MenuItem>
                  <MenuItem value="injured">Injured</MenuItem>
                  <MenuItem value="recovering">Recovering</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Notes"
                value={newAnimal.notes || ''}
                onChange={(e) => setNewAnimal({ ...newAnimal, notes: e.target.value })}
                multiline
                rows={2}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowEditDialog(false)}>Cancel</Button>
          <Button variant="contained" onClick={handleUpdateAnimal}>
            Update Animal
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default BetaAnimalsModule;
