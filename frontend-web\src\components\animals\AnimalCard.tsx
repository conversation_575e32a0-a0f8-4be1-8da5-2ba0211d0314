import React from 'react';
import { Box, Typography, Chip, useTheme, alpha } from '@mui/material';
import { motion } from 'framer-motion';
import { ModuleContentCard } from '../common';
import { Pets, Female, Male, Cake, LocalHospital, MonetizationOn } from '../../utils/iconImports';
import { Animal } from '../../types/animal';
import { useLanguage } from '../../contexts/LanguageContext';

interface AnimalCardProps {
  animal: Animal;
  onClick?: (animal: Animal) => void;
  delay?: number;
  selectable?: boolean;
  selected?: boolean;
  onSelect?: () => void;
}

/**
 * AnimalCard - A specialized card component for displaying animal information
 * Based on the BusinessAnalyticsCard design pattern
 */
const AnimalCard: React.FC<AnimalCardProps> = ({
  animal,
  onClick,
  delay = 0,
  selectable = false,
  selected = false,
  onSelect
}) => {
  const theme = useTheme();
  const { translate } = useLanguage();

  // Get gender icon
  const getGenderIcon = () => {
    return animal.gender?.toLowerCase() === 'female' ? 
      <Female sx={{ fontSize: 16, color: '#E91E63' }} /> : 
      <Male sx={{ fontSize: 16, color: '#2196F3' }} />;
  };

  // Get health status color
  const getHealthStatusColor = () => {
    switch (animal.healthStatus?.toLowerCase()) {
      case 'healthy':
        return theme.palette.success.main;
      case 'sick':
        return theme.palette.error.main;
      case 'injured':
        return theme.palette.warning.main;
      case 'pregnant':
        return theme.palette.info.main;
      default:
        return theme.palette.grey[500];
    }
  };

  return (
    <ModuleContentCard
      title={animal.name || `${animal.tagNumber || 'Unknown'}`}
      subtitle={`${animal.breed || 'Unknown breed'} ${animal.type || 'animal'}`}
      icon={<Pets />}
      module="animals"
      delay={delay}
      selectable={selectable}
      selected={selected}
      onSelect={onSelect}
      onAction={onClick ? () => onClick(animal) : undefined}
      actionLabel={onClick ? translate('common.view_details') : undefined}
      hoverEffect="scale"
    >
      <Box sx={{ p: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ mr: 2 }}>
            <Chip 
              icon={getGenderIcon()} 
              label={animal.gender || 'Unknown'} 
              size="small" 
              sx={{ 
                bgcolor: alpha(animal.gender?.toLowerCase() === 'female' ? '#E91E63' : '#2196F3', 0.1),
                color: animal.gender?.toLowerCase() === 'female' ? '#E91E63' : '#2196F3',
                fontWeight: 'medium'
              }} 
            />
          </Box>
          <Box>
            <Chip 
              icon={<LocalHospital sx={{ fontSize: 16 }} />} 
              label={animal.healthStatus || 'Unknown'} 
              size="small" 
              sx={{ 
                bgcolor: alpha(getHealthStatusColor(), 0.1),
                color: getHealthStatusColor(),
                fontWeight: 'medium'
              }} 
            />
          </Box>
        </Box>

        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Cake sx={{ fontSize: 18, mr: 1, color: theme.palette.text.secondary }} />
            <Typography variant="body2" color="text.secondary">
              {translate('animals.birth_date')}: {animal.birthDate ? new Date(animal.birthDate).toLocaleDateString() : 'Unknown'}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <MonetizationOn sx={{ fontSize: 18, mr: 1, color: theme.palette.text.secondary }} />
            <Typography variant="body2" color="text.secondary">
              {translate('animals.value')}: R {animal.currentValue?.toLocaleString() || animal.purchasePrice?.toLocaleString() || 'Unknown'}
            </Typography>
          </Box>

          {animal.location && (
            <Typography variant="body2" color="text.secondary">
              {translate('animals.location')}: {animal.location}
            </Typography>
          )}
        </Box>
      </Box>
    </ModuleContentCard>
  );
};

export default AnimalCard;
