import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Chip,
  useTheme,
  alpha,
  IconButton,
  Tooltip,
  Paper
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  Analytics,
  PieChart,
  BarChart,
  Timeline,
  Refresh,
  Download,
  FilterList,
  DateRange
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart as RechartsBarChart,
  Bar,
  <PERSON><PERSON>hart as RechartsPieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from 'recharts';

interface AnalyticsData {
  totalAnimals: number;
  healthyAnimals: number;
  pregnantAnimals: number;
  revenue: number;
  expenses: number;
  profit: number;
  trends: {
    animals: number;
    health: number;
    breeding: number;
    financial: number;
  };
  chartData: {
    monthly: any[];
    species: any[];
    health: any[];
    breeding: any[];
    financial: any[];
    performance: any[];
  };
}

const AdvancedAnalyticsDashboard: React.FC = () => {
  const theme = useTheme();
  const [timeRange, setTimeRange] = useState('12months');
  const [selectedMetrics, setSelectedMetrics] = useState(['animals', 'health', 'breeding', 'financial']);
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);

  // Color palette for charts
  const colors = [
    theme.palette.primary.main,
    theme.palette.secondary.main,
    '#FF6B6B',
    '#4ECDC4',
    '#45B7D1',
    '#96CEB4',
    '#FFEAA7',
    '#DDA0DD'
  ];

  useEffect(() => {
    fetchAnalyticsData();
  }, [timeRange, selectedMetrics]);

  const fetchAnalyticsData = async () => {
    setLoading(true);
    try {
      // Simulate API call with mock data
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockData: AnalyticsData = {
        totalAnimals: 1247,
        healthyAnimals: 1189,
        pregnantAnimals: 89,
        revenue: 2450000,
        expenses: 1890000,
        profit: 560000,
        trends: {
          animals: 12.5,
          health: 8.3,
          breeding: 15.7,
          financial: 22.1
        },
        chartData: {
          monthly: [
            { month: 'Jan', animals: 1100, revenue: 180000, expenses: 140000, births: 45 },
            { month: 'Feb', animals: 1120, revenue: 195000, expenses: 145000, births: 52 },
            { month: 'Mar', animals: 1145, revenue: 210000, expenses: 155000, births: 48 },
            { month: 'Apr', animals: 1167, revenue: 225000, expenses: 160000, births: 55 },
            { month: 'May', animals: 1189, revenue: 240000, expenses: 165000, births: 61 },
            { month: 'Jun', animals: 1205, revenue: 255000, expenses: 170000, births: 58 },
            { month: 'Jul', animals: 1220, revenue: 270000, expenses: 175000, births: 63 },
            { month: 'Aug', animals: 1235, revenue: 285000, expenses: 180000, births: 67 },
            { month: 'Sep', animals: 1247, revenue: 300000, expenses: 185000, births: 72 },
            { month: 'Oct', animals: 1260, revenue: 315000, expenses: 190000, births: 69 },
            { month: 'Nov', animals: 1275, revenue: 330000, expenses: 195000, births: 74 },
            { month: 'Dec', animals: 1290, revenue: 345000, expenses: 200000, births: 78 }
          ],
          species: [
            { name: 'Cattle', value: 687, percentage: 55.1 },
            { name: 'Sheep', value: 312, percentage: 25.0 },
            { name: 'Goats', value: 156, percentage: 12.5 },
            { name: 'Pigs', value: 67, percentage: 5.4 },
            { name: 'Chickens', value: 25, percentage: 2.0 }
          ],
          health: [
            { status: 'Healthy', count: 1189, percentage: 95.3 },
            { status: 'Under Treatment', count: 34, percentage: 2.7 },
            { status: 'Quarantine', count: 15, percentage: 1.2 },
            { status: 'Critical', count: 9, percentage: 0.7 }
          ],
          breeding: [
            { month: 'Jan', pregnancies: 78, births: 45, success: 89.2 },
            { month: 'Feb', pregnancies: 82, births: 52, success: 91.5 },
            { month: 'Mar', pregnancies: 75, births: 48, success: 87.8 },
            { month: 'Apr', pregnancies: 89, births: 55, success: 93.1 },
            { month: 'May', pregnancies: 94, births: 61, success: 95.2 },
            { month: 'Jun', pregnancies: 87, births: 58, success: 90.7 }
          ],
          financial: [
            { category: 'Feed', amount: 450000, percentage: 23.8 },
            { category: 'Veterinary', amount: 280000, percentage: 14.8 },
            { category: 'Labor', amount: 520000, percentage: 27.5 },
            { category: 'Equipment', amount: 180000, percentage: 9.5 },
            { category: 'Utilities', amount: 120000, percentage: 6.3 },
            { category: 'Other', amount: 340000, percentage: 18.0 }
          ],
          performance: [
            { metric: 'Milk Production', score: 85, target: 90 },
            { metric: 'Feed Efficiency', score: 78, target: 80 },
            { metric: 'Breeding Success', score: 92, target: 85 },
            { metric: 'Health Score', score: 95, target: 90 },
            { metric: 'Mortality Rate', score: 88, target: 95 },
            { metric: 'Growth Rate', score: 82, target: 85 }
          ]
        }
      };
      
      setAnalyticsData(mockData);
    } catch (error) {
      console.error('Failed to fetch analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  if (loading || !analyticsData) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <Typography>Loading advanced analytics...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" fontWeight="bold" color="primary">
          Advanced Analytics Dashboard
        </Typography>
        
        <Box display="flex" gap={2} alignItems="center">
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Time Range</InputLabel>
            <Select
              value={timeRange}
              label="Time Range"
              onChange={(e) => setTimeRange(e.target.value)}
            >
              <MenuItem value="1month">1 Month</MenuItem>
              <MenuItem value="3months">3 Months</MenuItem>
              <MenuItem value="6months">6 Months</MenuItem>
              <MenuItem value="12months">12 Months</MenuItem>
              <MenuItem value="2years">2 Years</MenuItem>
            </Select>
          </FormControl>
          
          <Tooltip title="Refresh Data">
            <IconButton onClick={fetchAnalyticsData} color="primary">
              <Refresh />
            </IconButton>
          </Tooltip>
          
          <Button
            variant="outlined"
            startIcon={<Download />}
            onClick={() => import('../../services/exportService').then(({ exportAnalyticsData }) =>
              exportAnalyticsData(analyticsData, timeRange)
            )}
          >
            Export
          </Button>
        </Box>
      </Box>

      {/* Key Metrics Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <motion.div whileHover={{ scale: 1.02 }}>
            <Card sx={{ background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`, color: 'white' }}>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Box>
                    <Typography variant="h4" fontWeight="bold">
                      {analyticsData.totalAnimals.toLocaleString()}
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.9 }}>
                      Total Animals
                    </Typography>
                    <Box display="flex" alignItems="center" mt={1}>
                      <TrendingUp fontSize="small" />
                      <Typography variant="caption" ml={0.5}>
                        {formatPercentage(analyticsData.trends.animals)}
                      </Typography>
                    </Box>
                  </Box>
                  <Analytics sx={{ fontSize: 48, opacity: 0.7 }} />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <motion.div whileHover={{ scale: 1.02 }}>
            <Card sx={{ background: `linear-gradient(135deg, ${theme.palette.success.main}, ${theme.palette.success.dark})`, color: 'white' }}>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Box>
                    <Typography variant="h4" fontWeight="bold">
                      {((analyticsData.healthyAnimals / analyticsData.totalAnimals) * 100).toFixed(1)}%
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.9 }}>
                      Health Score
                    </Typography>
                    <Box display="flex" alignItems="center" mt={1}>
                      <TrendingUp fontSize="small" />
                      <Typography variant="caption" ml={0.5}>
                        {formatPercentage(analyticsData.trends.health)}
                      </Typography>
                    </Box>
                  </Box>
                  <PieChart sx={{ fontSize: 48, opacity: 0.7 }} />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <motion.div whileHover={{ scale: 1.02 }}>
            <Card sx={{ background: `linear-gradient(135deg, ${theme.palette.warning.main}, ${theme.palette.warning.dark})`, color: 'white' }}>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Box>
                    <Typography variant="h4" fontWeight="bold">
                      {analyticsData.pregnantAnimals}
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.9 }}>
                      Pregnant Animals
                    </Typography>
                    <Box display="flex" alignItems="center" mt={1}>
                      <TrendingUp fontSize="small" />
                      <Typography variant="caption" ml={0.5}>
                        {formatPercentage(analyticsData.trends.breeding)}
                      </Typography>
                    </Box>
                  </Box>
                  <Timeline sx={{ fontSize: 48, opacity: 0.7 }} />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <motion.div whileHover={{ scale: 1.02 }}>
            <Card sx={{ background: `linear-gradient(135deg, ${theme.palette.info.main}, ${theme.palette.info.dark})`, color: 'white' }}>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Box>
                    <Typography variant="h4" fontWeight="bold">
                      {formatCurrency(analyticsData.profit)}
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.9 }}>
                      Net Profit
                    </Typography>
                    <Box display="flex" alignItems="center" mt={1}>
                      <TrendingUp fontSize="small" />
                      <Typography variant="caption" ml={0.5}>
                        {formatPercentage(analyticsData.trends.financial)}
                      </Typography>
                    </Box>
                  </Box>
                  <BarChart sx={{ fontSize: 48, opacity: 0.7 }} />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>
      </Grid>

      {/* Charts Grid */}
      <Grid container spacing={3}>
        {/* Monthly Trends */}
        <Grid item xs={12} lg={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Monthly Trends
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={analyticsData.chartData.monthly}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <RechartsTooltip />
                  <Legend />
                  <Line type="monotone" dataKey="animals" stroke={colors[0]} strokeWidth={2} />
                  <Line type="monotone" dataKey="births" stroke={colors[1]} strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Species Distribution */}
        <Grid item xs={12} lg={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Species Distribution
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <RechartsPieChart>
                  <RechartsTooltip />
                  <Pie
                    data={analyticsData.chartData.species}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {analyticsData.chartData.species.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                    ))}
                  </Pie>
                </RechartsPieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AdvancedAnalyticsDashboard;
