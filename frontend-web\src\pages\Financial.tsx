import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useFinancialData } from '../hooks/useFinancialData';
import { useAuth } from '../contexts/AuthContext';
import Loading from '../components/Loading';
import BetaFinancialModule from '../components/beta/BetaFinancialModule';
import '../styles/theme/global-theme.css';
import styles from '../styles/components/financial.module.css';

const Financial: React.FC = () => {
  const { user } = useAuth();
  const { stats, budgets, transactions, loading, error } = useFinancialData();
  const [timeframe, setTimeframe] = useState('monthly');

  // Check if user is BETA user - using username for beta identification
  const isBetaUser = user?.username === 'Demo' || user?.username === 'demo';

  // If BETA user, render BETA Financial Module
  if (isBetaUser) {
    return <BetaFinancialModule />;
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  if (loading) {
    return <Loading />;
  }

  if (error) {
    return <div className="text-red-500">Error loading financial data: {error}</div>;
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className={styles.container}
    >
      <div className={styles.header}>
        <h1 className={styles.title}>Financial Management</h1>
        <div className={styles.controls}>
          <select
            className={styles.select}
            value={timeframe}
            onChange={(e) => setTimeframe(e.target.value)}
            title="Select timeframe for financial data"
            aria-label="Select timeframe for financial data"
          >
            <option value="weekly">Weekly</option>
            <option value="monthly">Monthly</option>
            <option value="quarterly">Quarterly</option>
            <option value="yearly">Yearly</option>
          </select>
          <button
            type="button"
            className={styles.buttonPrimary}
            aria-label="Generate financial report"
          >
            Generate Report
          </button>
        </div>
      </div>
      
      {/* Financial Summary */}
      <div className={styles.statsGrid}>
        <div className={styles.statCard}>
          <h3 className={styles.statLabel}>Total Revenue</h3>
          <p className={styles.statValuePositive}>{formatCurrency(stats.totalRevenue)}</p>
        </div>
        <div className={styles.statCard}>
          <h3 className={styles.statLabel}>Total Expenses</h3>
          <p className={styles.statValueNegative}>{formatCurrency(stats.totalExpenses)}</p>
        </div>
        <div className={styles.statCard}>
          <h3 className={styles.statLabel}>Net Profit</h3>
          <p className={styles.statValueNeutral}>{formatCurrency(stats.netProfit)}</p>
        </div>
        <div className={styles.statCard}>
          <h3 className={styles.statLabel}>Growth Rate</h3>
          <p className={styles.statValueNeutral}>{stats.growthRate}%</p>
        </div>
      </div>

      <div className={styles.contentGrid}>
        {/* Recent Transactions */}
        <div className={styles.card}>
          <div className={styles.cardHeader}>
            <h2 className={styles.cardTitle}>Recent Transactions</h2>
            <button type="button" className={styles.cardAction}>View All</button>
          </div>
          <div className={styles.cardContent}>
            <table className={styles.table}>
              <thead>
                <tr>
                  <th className={styles.tableHeader}>Date</th>
                  <th className={styles.tableHeader}>Description</th>
                  <th className={styles.tableHeader}>Amount</th>
                </tr>
              </thead>
              <tbody>
                {transactions.map(transaction => (
                  <tr key={transaction.id} className={styles.tableRow}>
                    <td className={styles.tableCell}>{new Date(transaction.date).toLocaleDateString()}</td>
                    <td className={styles.tableCell}>{transaction.description}</td>
                    <td className={transaction.type === 'income' ? styles.tableCellPositive : styles.tableCellNegative}>
                      {formatCurrency(transaction.amount)}
                    </td>
                  </tr>
                ))}
                {/* Add some placeholder rows if we have few transactions */}
                {transactions.length === 0 && (
                  <tr>
                    <td colSpan={3} className={styles.emptyState}>No recent transactions</td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Budget Overview */}
        <div className={styles.card}>
          <div className={styles.cardHeader}>
            <h2 className={styles.cardTitle}>Budget Overview</h2>
            <button type="button" className={styles.cardAction}>Manage Budgets</button>
          </div>
          <div className={styles.cardContent}>
            {budgets.map((budget) => (
              <div key={budget.category} className={styles.budgetItem}>
                <div className={styles.budgetHeader}>
                  <span className={styles.budgetCategory}>{budget.category}</span>
                  <span className={styles.budgetAmount}>{formatCurrency(budget.spent)} / {formatCurrency(budget.allocated)}</span>
                </div>
                <div className={styles.progressBar}>
                  <div
                    className={styles.progressFill}
                    style={{ width: `${budget.percentage}%` }}
                    role="progressbar"
                    aria-label={`Budget progress for ${budget.category}: ${budget.percentage}%`}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Financial Actions */}
      <div className={styles.actionGrid}>
        <div className={styles.actionCard}>
          <div className={styles.actionIcon}>📊</div>
          <h3 className={styles.actionTitle}>Financial Reports</h3>
          <p className={styles.actionDescription}>Generate detailed financial reports and statements</p>
          <button type="button" className={styles.actionButton}>
            Generate Reports
          </button>
        </div>
        <div className={styles.actionCard}>
          <div className={styles.actionIcon}>💰</div>
          <h3 className={styles.actionTitle}>Record Transaction</h3>
          <p className={styles.actionDescription}>Add new income or expense transactions</p>
          <button type="button" className={styles.actionButton}>
            New Transaction
          </button>
        </div>
        <div className={styles.actionCard}>
          <div className={styles.actionIcon}>📝</div>
          <h3 className={styles.actionTitle}>Budget Planning</h3>
          <p className={styles.actionDescription}>Create and manage budgets for different categories</p>
          <button type="button" className={styles.actionButton}>
            Manage Budgets
          </button>
        </div>
      </div>
    </motion.div>
  );
};

export default Financial;
