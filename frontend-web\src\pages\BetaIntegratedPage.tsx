import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Container,
  Typo<PERSON>,
  <PERSON>ton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  useTheme,
  alpha,
  <PERSON><PERSON>kbar,
  Alert,
} from '@mui/material';
import {
  Language,
  Palette,
  Login,
  PersonAdd,
  Logout,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import StunningBetaLanding from '../components/beta/StunningBetaLanding';
import { gradients, colors } from '../theme/modernTheme';

// Mock user data
interface User {
  id: string;
  username: string;
  role: 'beta' | 'enterprise' | 'admin';
  name: string;
}

const mockUsers: User[] = [
  { id: '1', username: 'Demo', role: 'beta', name: 'Demo User' },
  { id: '2', username: '<PERSON>', role: 'admin', name: '<PERSON><PERSON>' },
  { id: '3', username: 'Enterprise', role: 'enterprise', name: 'Enterprise User' },
];

const BetaIntegratedPage: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  
  // State management
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [showLoginDialog, setShowLoginDialog] = useState(false);
  const [showRegisterDialog, setShowRegisterDialog] = useState(false);
  const [showLanguageDialog, setShowLanguageDialog] = useState(false);
  const [showThemeDialog, setShowThemeDialog] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState('en');
  const [selectedTheme, setSelectedTheme] = useState('light');
  const [loginForm, setLoginForm] = useState({ username: '', password: '' });
  const [registerForm, setRegisterForm] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    farmName: '',
  });
  const [notification, setNotification] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'warning' | 'info';
  }>({ open: false, message: '', severity: 'info' });

  // South African Official Languages
  const languages = [
    { code: 'en', name: 'English', native: 'English' },
    { code: 'af', name: 'Afrikaans', native: 'Afrikaans' },
    { code: 'zu', name: 'Zulu', native: 'isiZulu' },
    { code: 'xh', name: 'Xhosa', native: 'isiXhosa' },
    { code: 'st', name: 'Sotho', native: 'Sesotho' },
    { code: 'tn', name: 'Tswana', native: 'Setswana' },
    { code: 'ss', name: 'Swati', native: 'siSwati' },
    { code: 've', name: 'Venda', native: 'Tshivenḓa' },
    { code: 'ts', name: 'Tsonga', native: 'Xitsonga' },
    { code: 'nr', name: 'Ndebele', native: 'isiNdebele' },
    { code: 'nso', name: 'Northern Sotho', native: 'Sepedi' },
  ];

  const themes = [
    { id: 'light', name: 'Light Theme', description: 'Clean and bright interface' },
    { id: 'dark', name: 'Dark Theme', description: 'Easy on the eyes' },
    { id: 'gradient', name: 'Gradient Theme', description: 'Colorful and dynamic' },
    { id: 'farm', name: 'Farm Theme', description: 'Nature-inspired colors' },
  ];

  // Auto-login demo user on page load
  useEffect(() => {
    const demoUser = mockUsers.find(u => u.username === 'Demo');
    if (demoUser) {
      setCurrentUser(demoUser);
    }
  }, []);

  const handleLogin = () => {
    const user = mockUsers.find(u => 
      u.username.toLowerCase() === loginForm.username.toLowerCase() &&
      (loginForm.password === '123' || loginForm.password === 'admin')
    );

    if (user) {
      setCurrentUser(user);
      setShowLoginDialog(false);
      setLoginForm({ username: '', password: '' });
      showNotification('Login successful!', 'success');
    } else {
      showNotification('Invalid credentials. Try Demo/123 or May Rakgama/admin', 'error');
    }
  };

  const handleRegister = () => {
    if (registerForm.password !== registerForm.confirmPassword) {
      showNotification('Passwords do not match', 'error');
      return;
    }

    const newUser: User = {
      id: Date.now().toString(),
      username: registerForm.username,
      role: 'beta',
      name: registerForm.username,
    };

    setCurrentUser(newUser);
    setShowRegisterDialog(false);
    setRegisterForm({
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
      farmName: '',
    });
    showNotification('Registration successful! Welcome to AgriIntel Beta!', 'success');
  };

  const handleLogout = () => {
    setCurrentUser(null);
    showNotification('Logged out successfully', 'info');
  };

  const handleModuleClick = (moduleId: string) => {
    // Navigate to specific module or show upgrade dialog
    // Financial and feeding are now available in BETA with limitations
    if (currentUser?.role === 'beta' && ['breeding', 'inventory', 'commercial', 'compliance'].includes(moduleId)) {
      showNotification('Upgrade to Enterprise Plan to access this module', 'warning');
    } else {
      navigate(`/dashboard/${moduleId}`);
    }
  };

  const showNotification = (message: string, severity: 'success' | 'error' | 'warning' | 'info') => {
    setNotification({ open: true, message, severity });
  };

  const handleLanguageChange = () => {
    setShowLanguageDialog(true);
  };

  const handleThemeChange = () => {
    setShowThemeDialog(true);
  };

  // If user is logged in, show the stunning beta landing
  if (currentUser) {
    return (
      <>
        <StunningBetaLanding
          userRole={currentUser.role}
          userName={currentUser.name}
          onModuleClick={handleModuleClick}
          onLanguageChange={handleLanguageChange}
          onThemeChange={handleThemeChange}
          onLogout={handleLogout}
        />

        {/* Language Selection Dialog */}
        <Dialog
          open={showLanguageDialog}
          onClose={() => setShowLanguageDialog(false)}
          maxWidth="sm"
          fullWidth
          PaperProps={{
            sx: {
              borderRadius: 4,
              background: gradients.cardBackground,
              backdropFilter: 'blur(20px)',
            },
          }}
        >
          <DialogTitle sx={{ textAlign: 'center', pb: 1 }}>
            <Typography variant="h5" fontWeight="bold">
              Select Language / Kies Taal
            </Typography>
          </DialogTitle>
          <DialogContent>
            <FormControl fullWidth sx={{ mt: 2 }}>
              <InputLabel>Language</InputLabel>
              <Select
                value={selectedLanguage}
                onChange={(e) => setSelectedLanguage(e.target.value)}
                label="Language"
              >
                {languages.map((lang) => (
                  <MenuItem key={lang.code} value={lang.code}>
                    <Box display="flex" justifyContent="space-between" width="100%">
                      <span>{lang.name}</span>
                      <span style={{ fontStyle: 'italic', opacity: 0.7 }}>
                        {lang.native}
                      </span>
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </DialogContent>
          <DialogActions sx={{ p: 3 }}>
            <Button onClick={() => setShowLanguageDialog(false)}>
              Cancel
            </Button>
            <Button
              variant="contained"
              onClick={() => {
                setShowLanguageDialog(false);
                showNotification(`Language changed to ${languages.find(l => l.code === selectedLanguage)?.name}`, 'success');
              }}
            >
              Apply
            </Button>
          </DialogActions>
        </Dialog>

        {/* Theme Selection Dialog */}
        <Dialog
          open={showThemeDialog}
          onClose={() => setShowThemeDialog(false)}
          maxWidth="sm"
          fullWidth
          PaperProps={{
            sx: {
              borderRadius: 4,
              background: gradients.cardBackground,
              backdropFilter: 'blur(20px)',
            },
          }}
        >
          <DialogTitle sx={{ textAlign: 'center', pb: 1 }}>
            <Typography variant="h5" fontWeight="bold">
              Choose Theme
            </Typography>
          </DialogTitle>
          <DialogContent>
            <FormControl fullWidth sx={{ mt: 2 }}>
              <InputLabel>Theme</InputLabel>
              <Select
                value={selectedTheme}
                onChange={(e) => setSelectedTheme(e.target.value)}
                label="Theme"
              >
                {themes.map((theme) => (
                  <MenuItem key={theme.id} value={theme.id}>
                    <Box>
                      <Typography variant="body1">{theme.name}</Typography>
                      <Typography variant="caption" color="text.secondary">
                        {theme.description}
                      </Typography>
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </DialogContent>
          <DialogActions sx={{ p: 3 }}>
            <Button onClick={() => setShowThemeDialog(false)}>
              Cancel
            </Button>
            <Button
              variant="contained"
              onClick={() => {
                setShowThemeDialog(false);
                showNotification(`Theme changed to ${themes.find(t => t.id === selectedTheme)?.name}`, 'success');
              }}
            >
              Apply
            </Button>
          </DialogActions>
        </Dialog>

        {/* Notification Snackbar */}
        <Snackbar
          open={notification.open}
          autoHideDuration={4000}
          onClose={() => setNotification({ ...notification, open: false })}
          anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <Alert
            onClose={() => setNotification({ ...notification, open: false })}
            severity={notification.severity}
            sx={{ width: '100%' }}
          >
            {notification.message}
          </Alert>
        </Snackbar>
      </>
    );
  }

  // Login/Register Landing Page
  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: gradients.pageBackground,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* Background Elements */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: `url('/images/farm-landscape.jpg')`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          opacity: 0.1,
          zIndex: 1,
        }}
      />

      <Container maxWidth="sm" sx={{ position: 'relative', zIndex: 10 }}>
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <Box
            sx={{
              background: gradients.cardBackground,
              backdropFilter: 'blur(20px)',
              borderRadius: 4,
              p: 4,
              textAlign: 'center',
              border: `1px solid ${alpha(colors.primary[300], 0.3)}`,
              boxShadow: `0 20px 60px ${alpha(colors.primary[500], 0.15)}`,
            }}
          >
            {/* Logo and Title */}
            <Typography
              variant="h2"
              sx={{
                fontWeight: 800,
                mb: 1,
                background: gradients.primary,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              AgriIntel
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: theme.palette.text.secondary,
                mb: 4,
                fontWeight: 400,
              }}
            >
              Intelligent Livestock Management Platform
            </Typography>

            {/* Action Buttons */}
            <Box display="flex" flexDirection="column" gap={2} mb={4}>
              <Button
                variant="contained"
                size="large"
                startIcon={<Login />}
                onClick={() => setShowLoginDialog(true)}
                sx={{
                  py: 1.5,
                  fontSize: '1.1rem',
                  borderRadius: 3,
                  background: gradients.primary,
                  '&:hover': {
                    background: gradients.primaryDark,
                    transform: 'translateY(-2px)',
                  },
                }}
              >
                Sign In
              </Button>
              
              <Button
                variant="outlined"
                size="large"
                startIcon={<PersonAdd />}
                onClick={() => setShowRegisterDialog(true)}
                sx={{
                  py: 1.5,
                  fontSize: '1.1rem',
                  borderRadius: 3,
                  borderColor: colors.primary[400],
                  color: colors.primary[600],
                  '&:hover': {
                    borderColor: colors.primary[600],
                    background: alpha(colors.primary[500], 0.05),
                    transform: 'translateY(-2px)',
                  },
                }}
              >
                Create Account
              </Button>
            </Box>

            <Divider sx={{ my: 3 }}>
              <Typography variant="body2" color="text.secondary">
                Quick Access
              </Typography>
            </Divider>

            {/* Quick Login Options */}
            <Box display="flex" flexDirection="column" gap={1}>
              <Button
                variant="text"
                size="small"
                onClick={() => {
                  setLoginForm({ username: 'Demo', password: '123' });
                  handleLogin();
                }}
                sx={{ color: colors.primary[600] }}
              >
                Demo User (Beta Access)
              </Button>
              <Button
                variant="text"
                size="small"
                onClick={() => {
                  setLoginForm({ username: 'May Rakgama', password: 'admin' });
                  handleLogin();
                }}
                sx={{ color: colors.secondary[600] }}
              >
                May Rakgama (Admin Access)
              </Button>
            </Box>

            {/* Language and Theme Buttons */}
            <Box display="flex" justifyContent="center" gap={2} mt={4}>
              <Button
                variant="outlined"
                size="small"
                startIcon={<Language />}
                onClick={handleLanguageChange}
                sx={{ borderRadius: 2 }}
              >
                Language
              </Button>
              <Button
                variant="outlined"
                size="small"
                startIcon={<Palette />}
                onClick={handleThemeChange}
                sx={{ borderRadius: 2 }}
              >
                Theme
              </Button>
            </Box>
          </Box>
        </motion.div>
      </Container>

      {/* Login Dialog */}
      <Dialog
        open={showLoginDialog}
        onClose={() => setShowLoginDialog(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 4,
            background: gradients.cardBackground,
            backdropFilter: 'blur(20px)',
          },
        }}
      >
        <DialogTitle>
          <Typography variant="h5" fontWeight="bold" textAlign="center">
            Sign In to AgriIntel
          </Typography>
        </DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="Username"
            value={loginForm.username}
            onChange={(e) => setLoginForm({ ...loginForm, username: e.target.value })}
            margin="normal"
            sx={{ mb: 2 }}
          />
          <TextField
            fullWidth
            label="Password"
            type="password"
            value={loginForm.password}
            onChange={(e) => setLoginForm({ ...loginForm, password: e.target.value })}
            margin="normal"
          />
        </DialogContent>
        <DialogActions sx={{ p: 3 }}>
          <Button onClick={() => setShowLoginDialog(false)}>
            Cancel
          </Button>
          <Button variant="contained" onClick={handleLogin}>
            Sign In
          </Button>
        </DialogActions>
      </Dialog>

      {/* Register Dialog */}
      <Dialog
        open={showRegisterDialog}
        onClose={() => setShowRegisterDialog(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 4,
            background: gradients.cardBackground,
            backdropFilter: 'blur(20px)',
          },
        }}
      >
        <DialogTitle>
          <Typography variant="h5" fontWeight="bold" textAlign="center">
            Create Your AgriIntel Account
          </Typography>
        </DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="Username"
            value={registerForm.username}
            onChange={(e) => setRegisterForm({ ...registerForm, username: e.target.value })}
            margin="normal"
          />
          <TextField
            fullWidth
            label="Email"
            type="email"
            value={registerForm.email}
            onChange={(e) => setRegisterForm({ ...registerForm, email: e.target.value })}
            margin="normal"
          />
          <TextField
            fullWidth
            label="Farm Name"
            value={registerForm.farmName}
            onChange={(e) => setRegisterForm({ ...registerForm, farmName: e.target.value })}
            margin="normal"
          />
          <TextField
            fullWidth
            label="Password"
            type="password"
            value={registerForm.password}
            onChange={(e) => setRegisterForm({ ...registerForm, password: e.target.value })}
            margin="normal"
          />
          <TextField
            fullWidth
            label="Confirm Password"
            type="password"
            value={registerForm.confirmPassword}
            onChange={(e) => setRegisterForm({ ...registerForm, confirmPassword: e.target.value })}
            margin="normal"
          />
        </DialogContent>
        <DialogActions sx={{ p: 3 }}>
          <Button onClick={() => setShowRegisterDialog(false)}>
            Cancel
          </Button>
          <Button variant="contained" onClick={handleRegister}>
            Create Account
          </Button>
        </DialogActions>
      </Dialog>

      {/* Notification Snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={4000}
        onClose={() => setNotification({ ...notification, open: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, open: false })}
          severity={notification.severity}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default BetaIntegratedPage;
