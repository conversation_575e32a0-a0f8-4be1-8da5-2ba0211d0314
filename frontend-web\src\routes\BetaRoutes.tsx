import React from 'react';
import { Routes, Route } from 'react-router-dom';
import BetaAnimalsModule from '../components/beta/BetaAnimalsModule';
import BetaFeedModule from '../components/beta/BetaFeedModule';
import BetaReportsModule from '../components/beta/BetaReportsModule';
import SubscriptionPlans from '../components/subscription/SubscriptionPlans';
import ServiceMarketplace from '../components/marketplace/ServiceMarketplace';
import AutoTaskSystem from '../components/marketplace/AutoTaskSystem';
import ContractPaymentSystem from '../components/marketplace/ContractPaymentSystem';

const BetaRoutes: React.FC = () => {
  return (
    <Routes>
      {/* BETA Module Routes */}
      <Route path="/beta/animals" element={<BetaAnimalsModule userTier="beta" />} />
      <Route path="/beta/feed" element={<BetaFeedModule userTier="beta" />} />
      <Route path="/beta/reports" element={<BetaReportsModule userTier="beta" />} />
      
      {/* Subscription Routes */}
      <Route path="/subscription" element={<SubscriptionPlans />} />
      <Route path="/subscription/plans" element={<SubscriptionPlans />} />
      
      {/* Professional/Enterprise Routes */}
      <Route path="/marketplace" element={<ServiceMarketplace />} />
      <Route path="/marketplace/services" element={<ServiceMarketplace />} />
      <Route path="/marketplace/auto-tasks" element={<AutoTaskSystem />} />
      <Route path="/marketplace/contracts" element={<ContractPaymentSystem />} />
      
      {/* Default redirect to subscription for upgrade prompts */}
      <Route path="/upgrade" element={<SubscriptionPlans />} />
    </Routes>
  );
};

export default BetaRoutes;
