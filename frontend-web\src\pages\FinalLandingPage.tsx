import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import '../styles/final-landing.css';
import '../styles/theme/global-theme.css';
import landingStyles from '../styles/components/landing-page.module.css';

// Modern CSS animations and styles
const styles = `
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-30px) rotate(10deg); }
  }

  @keyframes slideIn {
    0% { transform: translateX(-100px); opacity: 0; }
    100% { transform: translateX(0); opacity: 1; }
  }

  @keyframes fadeInUp {
    0% { transform: translateY(50px); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
  }

  @keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
  }

  @keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  @keyframes morphShape {
    0%, 100% { border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%; }
    25% { border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%; }
    50% { border-radius: 50% 50% 25% 75% / 25% 75% 25% 75%; }
    75% { border-radius: 25% 75% 50% 50% / 75% 25% 75% 25%; }
  }

  .full-screen-container {
    width: 100vw;
    min-height: 100vh;
    overflow-x: hidden;
  }

  .section-full {
    width: 100%;
    padding: 0;
    margin: 0;
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const existingStyle = document.getElementById('landing-styles');
  if (existingStyle) {
    existingStyle.remove();
  }
  const styleSheet = document.createElement('style');
  styleSheet.id = 'landing-styles';
  styleSheet.type = 'text/css';
  styleSheet.innerText = styles;
  document.head.appendChild(styleSheet);
}

const FinalLandingPage: React.FC = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('features');
  const [currentImageIndex, setCurrentImageIndex] = useState(0);


  // Professional livestock images for background showcase
  const livestockImages = [
    'https://wallpaperaccess.com/full/7985150.jpg', // Beautiful cattle in field
    'https://wallpaperaccess.com/full/2973889.jpg', // Professional cow portrait
    'https://wallpaperaccess.com/full/7985153.jpg', // Cattle herd landscape
    'https://wallpaperaccess.com/full/6946250.jpg', // Premium cattle wallpaper
    'https://wallpaperaccess.com/full/7985155.jpg', // Cattle in natural setting
    'https://wallpaperaccess.com/full/1137850.jpg', // Cute cow close-up
    'https://wallpaperaccess.com/full/7985186.jpg', // Cattle ranch scene
    'https://wallpaperaccess.com/full/7985190.jpg', // Cows in Alps mountains
    'https://wallpaperaccess.com/full/3174996.jpg', // Longhorn cattle HD
    'https://wallpaperaccess.com/full/2390117.jpg', // Baby farm animals
    'https://wallpaperaccess.com/full/4913590.jpg', // Professional animal wallpaper
    'https://wallpaperaccess.com/full/7985232.jpg'  // Beautiful livestock scene
  ];

  // Auto-rotate images
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prev) => (prev + 1) % livestockImages.length);
    }, 4000);
    return () => clearInterval(interval);
  }, [livestockImages.length]);



  const features = [
    { icon: '🐄', title: 'Smart Livestock Tracking', description: 'Advanced RFID and IoT sensors for real-time monitoring of cattle, sheep, and goats across your South African farm operations.' },
    { icon: '🩺', title: 'AI Health Management', description: 'AI-powered health diagnostics and predictive analytics to prevent diseases and reduce veterinary costs by up to 40%.' },
    { icon: '📊', title: 'Advanced Analytics', description: 'Comprehensive insights and reporting tools specifically designed for South African livestock operations.' },
    { icon: '💰', title: 'Financial Optimization', description: 'Smart financial planning and cost optimization tools tailored for South African farming with ZAR currency support.' },
    { icon: '🔒', title: 'Enterprise Security', description: 'Bank-grade security with local South African data centers, cloud backup, and 99.9% uptime guarantee.' },
    { icon: '🌱', title: 'Sustainable Farming', description: 'Environmental monitoring and sustainable farming practices designed for South African climate conditions.' }
  ];

  const stats = [
    { number: '15K+', label: 'South African Farmers', icon: '👨‍🌾' },
    { number: '750K+', label: 'Livestock Tracked', icon: '🐄' },
    { number: '99.9%', label: 'System Uptime', icon: '⚡' },
    { number: '40%', label: 'Cost Reduction', icon: '💰' }
  ];

  const subscriptionTiers = [
    {
      name: 'Beta Access',
      price: 'Free',
      duration: '30 days trial',
      description: 'Perfect for small-scale South African farmers',
      features: [
        '🐄 Up to 50 animals',
        '📱 Mobile app access',
        '📊 Basic health monitoring',
        '📈 Simple reports',
        '📧 Email support',
        '🇿🇦 South African language support'
      ],
      buttonText: 'START BETA',
      buttonAction: () => navigate('/beta-login'),
      popular: false
    },
    {
      name: 'Professional',
      price: 'R299',
      duration: 'per month',
      description: 'For growing commercial farms across SA',
      features: [
        '🐄 Up to 500 animals',
        '🤖 AI health analytics',
        '💰 Financial management (ZAR)',
        '🧬 Breeding optimization',
        '📞 Priority support',
        '📊 Custom reports',
        '🔗 API access',
        '🌍 Multi-location support'
      ],
      buttonText: 'GO LIVE',
      buttonAction: () => navigate('/login'),
      popular: true
    },
    {
      name: 'Enterprise',
      price: 'R599',
      duration: 'per month',
      description: 'For large commercial operations & cooperatives',
      features: [
        '🐄 Unlimited animals',
        '🤖 AI-powered insights',
        '🏢 Multi-farm management',
        '📊 Advanced analytics',
        '👨‍💼 Dedicated account manager',
        '🔧 Custom integrations',
        '🏷️ White-label options',
        '🎓 Training & onboarding'
      ],
      buttonText: 'GO LIVE',
      buttonAction: () => navigate('/login'),
      popular: false
    }
  ];



  // Remove inline styles - now using CSS modules

  return (
    <div className="final-landing">
      {/* Rotating Background Images with Green Gradient Blend */}
      <div
        className="final-landing-bg-image"
        style={{
          backgroundImage: `url(${livestockImages[currentImageIndex]})`,
          transition: 'all 2s ease-in-out'
        }}
      />

      {/* Green Gradient Overlay Blend */}
      <div className="final-landing-gradient-overlay" />

      {/* Subtle Pattern Overlay */}
      <div className="final-landing-pattern-overlay" />

      {/* Navigation */}
      <nav className="final-landing-nav">
        <div className="final-landing-brand">
          🌾 AgriIntel
          <span className="final-landing-brand-beta">
            BETA
          </span>
        </div>
        <div className="final-landing-nav-buttons">
          <button
            className="final-landing-btn-primary"
            onClick={() => navigate('/beta-login')}
          >
            🚀 Try Beta
          </button>
          <button
            className="final-landing-btn-secondary"
            onClick={() => navigate('/login')}
          >
            ⚡ Go Live
          </button>
        </div>
      </nav>

      {/* Simple Centered Hero Section */}
      <section className="final-landing-hero">
        {/* Main Content */}
        <div className="final-landing-hero-content">
          {/* Badge */}
          <div className="final-landing-badge">
            🏆 SOUTH AFRICA'S #1 LIVESTOCK PLATFORM
          </div>

          {/* Main Heading */}
          <h1 className="final-landing-hero-title">
            Smart Livestock Management
          </h1>

          <p className="final-landing-hero-subtitle">
            Transform your farming operations with AI-powered insights. Join 15,000+ South African farmers maximizing productivity and reducing costs by 40%.
          </p>

          {/* Action Buttons */}
          <div className="final-landing-hero-buttons">
            <button
              className="final-landing-hero-btn-primary"
              onClick={() => navigate('/beta-login')}
            >
              🚀 Start Free Trial
            </button>

            <button
              className="final-landing-hero-btn-secondary"
              onClick={() => navigate('/login')}
            >
              ⚡ Watch Demo
            </button>
          </div>

          {/* Simple Stats */}
          <div className="final-landing-stats">
            {[
              { number: '15K+', label: 'Active Farmers' },
              { number: '40%', label: 'Cost Reduction' },
              { number: '24/7', label: 'AI Monitoring' }
            ].map((stat, index) => (
              <div key={index} className="final-landing-stat">
                <div className="final-landing-stat-number">
                  {stat.number}
                </div>
                <div className="final-landing-stat-label">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section - Full Width */}
      <section className="section-full" style={{
        width: '100%',
        padding: '6rem 4rem',
        background: 'rgba(0, 0, 0, 0.1)',
        backdropFilter: 'blur(10px)'
      }}>
        <div style={{
          maxWidth: '1600px',
          margin: '0 auto',
          textAlign: 'center',
          marginBottom: '4rem'
        }}>
          <h2 style={{
            fontSize: 'clamp(2.5rem, 5vw, 4rem)',
            marginBottom: '1.5rem',
            fontWeight: '800',
            color: 'white',
            textShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'
          }}>
            Why Choose AgriIntel?
          </h2>
          <p style={{
            fontSize: '1.2rem',
            opacity: 0.9,
            maxWidth: '700px',
            margin: '0 auto',
            lineHeight: 1.6,
            color: 'rgba(255, 255, 255, 0.9)'
          }}>
            Comprehensive livestock management powered by cutting-edge AI technology
          </p>
        </div>

        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
          gap: '2rem',
          maxWidth: '1600px',
          margin: '0 auto'
        }}>
          {features.map((feature, index) => (
            <div
              key={index}
              style={{
                background: 'rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(30px)',
                padding: '2.5rem',
                borderRadius: '20px',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2)',
                transition: 'all 0.4s ease',
                position: 'relative',
                overflow: 'hidden',
                cursor: 'pointer',
                animation: `fadeInUp 0.6s ease-out ${index * 0.1}s both`
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.transform = 'translateY(-8px) scale(1.02)';
                e.currentTarget.style.boxShadow = '0 20px 60px rgba(255, 255, 255, 0.2)';
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.15)';
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.transform = 'translateY(0) scale(1)';
                e.currentTarget.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.2)';
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
              }}
            >
              <div style={{
                fontSize: '2.5rem',
                marginBottom: '1.5rem',
                filter: 'drop-shadow(0 0 15px rgba(255, 255, 255, 0.3))'
              }}>
                {feature.icon}
              </div>
              <h3 style={{
                fontSize: '1.4rem',
                marginBottom: '1rem',
                fontWeight: '700',
                color: 'white'
              }}>
                {feature.title}
              </h3>
              <p style={{
                opacity: 0.9,
                lineHeight: 1.6,
                fontSize: '1rem',
                color: 'rgba(255, 255, 255, 0.9)'
              }}>
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </section>

      {/* Livestock Showcase Section */}
      <section style={{
        background: 'rgba(0,0,0,0.3)',
        padding: '4rem 2rem',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* Background Image */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `url(${livestockImages[currentImageIndex]})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          opacity: 0.3,
          transition: 'all 1s ease-in-out',
          zIndex: 0
        }} />

        {/* Overlay */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'linear-gradient(135deg, rgba(27, 94, 32, 0.8) 0%, rgba(46, 125, 50, 0.6) 100%)',
          zIndex: 1
        }} />

        <div style={{
          maxWidth: '1400px',
          margin: '0 auto',
          position: 'relative',
          zIndex: 2,
          textAlign: 'center'
        }}>
          <h2 style={{
            fontSize: '3rem',
            marginBottom: '2rem',
            background: 'linear-gradient(45deg, #FFD700, #FFA500)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            textShadow: '2px 2px 4px rgba(0,0,0,0.5)'
          }}>
            🐄 REAL SOUTH AFRICAN LIVESTOCK SUCCESS
          </h2>

          <p style={{
            fontSize: '1.5rem',
            marginBottom: '3rem',
            opacity: 0.95,
            maxWidth: '800px',
            margin: '0 auto 3rem',
            textShadow: '1px 1px 2px rgba(0,0,0,0.7)'
          }}>
            See how AgriIntel transforms real farms across South Africa with cutting-edge technology
          </p>

          {/* Image Gallery Grid */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '2rem',
            marginBottom: '3rem'
          }}>
            {livestockImages.slice(0, 6).map((image, index) => (
              <div
                key={index}
                style={{
                  position: 'relative',
                  height: '250px',
                  borderRadius: '16px',
                  overflow: 'hidden',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  border: currentImageIndex === index ? '3px solid #FFD700' : '1px solid rgba(255,255,255,0.2)',
                  boxShadow: currentImageIndex === index
                    ? '0 8px 32px rgba(255, 215, 0, 0.4)'
                    : '0 4px 16px rgba(0,0,0,0.3)'
                }}
                onClick={() => setCurrentImageIndex(index)}
                onMouseOver={(e) => {
                  e.currentTarget.style.transform = 'scale(1.05)';
                  e.currentTarget.style.boxShadow = '0 12px 40px rgba(255, 215, 0, 0.3)';
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.transform = 'scale(1)';
                  e.currentTarget.style.boxShadow = currentImageIndex === index
                    ? '0 8px 32px rgba(255, 215, 0, 0.4)'
                    : '0 4px 16px rgba(0,0,0,0.3)';
                }}
              >
                <img
                  src={image}
                  alt={`Livestock ${index + 1}`}
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover'
                  }}
                />
                <div style={{
                  position: 'absolute',
                  bottom: 0,
                  left: 0,
                  right: 0,
                  background: 'linear-gradient(transparent, rgba(0,0,0,0.8))',
                  color: 'white',
                  padding: '1rem',
                  textAlign: 'center'
                }}>
                  <div style={{ fontWeight: 'bold', fontSize: '1.1rem' }}>
                    {['Premium Cattle', 'Dairy Excellence', 'Ranch Operations', 'Livestock Health', 'Farm Management', 'Smart Monitoring'][index]}
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="final-landing-hero-buttons">
            <button
              className={landingStyles.primaryButton}
              onClick={() => navigate('/beta-login')}
            >
              🚀 START YOUR SUCCESS STORY
            </button>
            <button
              className={landingStyles.secondaryButton}
              onClick={() => navigate('/login')}
            >
              📊 VIEW LIVE DEMO
            </button>
          </div>
        </div>
      </section>

      {/* Simple Tabbed Content Section with Unified Background */}
      <section style={{
        background: 'rgba(0, 0, 0, 0.2)',
        backdropFilter: 'blur(10px)',
        padding: '4rem 2rem',
        position: 'relative',
        zIndex: 10,
        // Unified background image for all tabs
        backgroundImage: `url(${livestockImages[currentImageIndex]})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundAttachment: 'fixed'
      }}>
        {/* Enhanced gradient overlay for better text readability */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: `linear-gradient(135deg,
            rgba(27, 94, 32, 0.9) 0%,
            rgba(46, 125, 50, 0.85) 25%,
            rgba(76, 175, 80, 0.8) 50%,
            rgba(139, 195, 74, 0.85) 75%,
            rgba(67, 160, 71, 0.9) 100%
          )`,
          zIndex: 1
        }} />

        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          position: 'relative',
          zIndex: 2
        }}>
          {/* Enhanced Tab Navigation with AgriIntel Theme */}
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            gap: '1rem',
            marginBottom: '3rem',
            flexWrap: 'wrap'
          }}>
            {[
              { key: 'features', label: 'Features', icon: '⚡' },
              { key: 'pricing', label: 'Pricing', icon: '💎' },
              { key: 'about', label: 'About', icon: '🌟' },
              { key: 'contact', label: 'Contact', icon: '📞' },
              { key: 'services', label: 'Services', icon: '🔧' }
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key)}
                style={{
                  padding: '1rem 2rem',
                  background: activeTab === tab.key
                    ? 'linear-gradient(135deg, rgba(255, 215, 0, 0.3), rgba(255, 193, 7, 0.2))'
                    : 'rgba(255, 255, 255, 0.1)',
                  color: 'white',
                  border: activeTab === tab.key
                    ? '2px solid rgba(255, 215, 0, 0.6)'
                    : '1px solid rgba(255, 255, 255, 0.2)',
                  borderRadius: '25px',
                  cursor: 'pointer',
                  fontWeight: '600',
                  fontSize: '1rem',
                  transition: 'all 0.3s ease',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  backdropFilter: 'blur(15px)',
                  boxShadow: activeTab === tab.key
                    ? '0 8px 25px rgba(255, 215, 0, 0.3)'
                    : '0 4px 15px rgba(0, 0, 0, 0.2)'
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.background = 'linear-gradient(135deg, rgba(255, 215, 0, 0.25), rgba(255, 193, 7, 0.15))';
                  e.currentTarget.style.transform = 'translateY(-2px)';
                  e.currentTarget.style.boxShadow = '0 8px 25px rgba(255, 215, 0, 0.4)';
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.background = activeTab === tab.key
                    ? 'linear-gradient(135deg, rgba(255, 215, 0, 0.3), rgba(255, 193, 7, 0.2))'
                    : 'rgba(255, 255, 255, 0.1)';
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = activeTab === tab.key
                    ? '0 8px 25px rgba(255, 215, 0, 0.3)'
                    : '0 4px 15px rgba(0, 0, 0, 0.2)';
                }}
              >
                <span>{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <div style={{ textAlign: 'center' }}>
            {activeTab === 'features' && (
              <div>
                <h2 style={{ 
                  fontSize: '2.5rem', 
                  marginBottom: '3rem',
                  background: 'linear-gradient(45deg, #FFD700, #FFA500)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}>
                  Powerful Features for Modern Farmers
                </h2>
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',
                  gap: '2rem'
                }}>
                  {features.map((feature, index) => (
                    <div 
                      key={index} 
                      style={{
                        ...cardStyle,
                        cursor: 'pointer'
                      }}
                      onMouseOver={(e) => {
                        e.currentTarget.style.transform = 'translateY(-8px)';
                        e.currentTarget.style.background = 'rgba(255,255,255,0.15)';
                      }}
                      onMouseOut={(e) => {
                        e.currentTarget.style.transform = 'translateY(0)';
                        e.currentTarget.style.background = 'rgba(255,255,255,0.1)';
                      }}
                    >
                      <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>{feature.icon}</div>
                      <h3 style={{ 
                        marginBottom: '1rem', 
                        fontSize: '1.3rem',
                        color: '#FFD700'
                      }}>
                        {feature.title}
                      </h3>
                      <p style={{ opacity: 0.9, lineHeight: 1.6 }}>{feature.description}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'pricing' && (
              <div style={{ animation: 'fadeInUp 0.6s ease-out' }}>
                <h2 style={{
                  fontSize: 'clamp(2rem, 4vw, 3rem)',
                  marginBottom: '2rem',
                  color: 'white',
                  fontWeight: '800'
                }}>
                  Choose Your Perfect Plan
                </h2>
                <p style={{
                  fontSize: '1.1rem',
                  opacity: 0.9,
                  marginBottom: '3rem',
                  color: 'rgba(255, 255, 255, 0.9)'
                }}>
                  Start free, upgrade when you're ready to scale
                </p>
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(380px, 1fr))',
                  gap: '2rem',
                  maxWidth: '1400px',
                  margin: '0 auto'
                }}>
                  {subscriptionTiers.map((tier, index) => (
                    <div
                      key={index}
                      style={{
                        background: tier.popular
                          ? 'linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.1))'
                          : 'rgba(255, 255, 255, 0.1)',
                        backdropFilter: 'blur(30px)',
                        padding: '2.5rem',
                        borderRadius: '25px',
                        border: tier.popular ? '2px solid rgba(255, 255, 255, 0.3)' : '1px solid rgba(255,255,255,0.2)',
                        position: 'relative',
                        cursor: 'pointer',
                        transform: tier.popular ? 'scale(1.05)' : 'scale(1)',
                        transition: 'all 0.4s ease',
                        boxShadow: tier.popular
                          ? '0 20px 60px rgba(255, 255, 255, 0.2)'
                          : '0 8px 32px rgba(0, 0, 0, 0.2)'
                      }}
                      onMouseOver={(e) => {
                        e.currentTarget.style.transform = tier.popular ? 'scale(1.08)' : 'scale(1.03)';
                        e.currentTarget.style.boxShadow = '0 25px 80px rgba(255, 255, 255, 0.3)';
                      }}
                      onMouseOut={(e) => {
                        e.currentTarget.style.transform = tier.popular ? 'scale(1.05)' : 'scale(1)';
                        e.currentTarget.style.boxShadow = tier.popular
                          ? '0 20px 60px rgba(255, 255, 255, 0.2)'
                          : '0 8px 32px rgba(0, 0, 0, 0.2)';
                      }}
                    >
                      {tier.popular && (
                        <div style={{
                          position: 'absolute',
                          top: '-15px',
                          left: '50%',
                          transform: 'translateX(-50%)',
                          background: 'linear-gradient(135deg, #ffffff, rgba(255,255,255,0.9))',
                          color: '#667eea',
                          padding: '0.5rem 1.5rem',
                          borderRadius: '25px',
                          fontWeight: '800',
                          fontSize: '0.8rem',
                          letterSpacing: '1px',
                          boxShadow: '0 4px 20px rgba(255, 255, 255, 0.3)'
                        }}>
                          ⭐ MOST POPULAR
                        </div>
                      )}
                      <h3 style={{
                        marginBottom: '1rem',
                        fontSize: '1.6rem',
                        color: 'white',
                        fontWeight: '700'
                      }}>
                        {tier.name}
                      </h3>
                      <div style={{
                        fontSize: '3rem',
                        fontWeight: '900',
                        color: 'white',
                        marginBottom: '0.5rem',
                        textShadow: '0 2px 10px rgba(0, 0, 0, 0.3)'
                      }}>
                        {tier.price}
                      </div>
                      <div style={{
                        opacity: 0.8,
                        marginBottom: '2rem',
                        fontSize: '1rem',
                        color: 'rgba(255, 255, 255, 0.8)'
                      }}>
                        {tier.duration}
                      </div>
                      <p style={{
                        marginBottom: '2rem',
                        opacity: 0.9,
                        fontSize: '1rem',
                        lineHeight: 1.6,
                        color: 'rgba(255, 255, 255, 0.9)'
                      }}>
                        {tier.description}
                      </p>
                      <ul style={{
                        listStyle: 'none',
                        padding: 0,
                        marginBottom: '2.5rem',
                        textAlign: 'left'
                      }}>
                        {tier.features.map((feature, i) => (
                          <li key={i} style={{
                            padding: '0.8rem 0',
                            borderBottom: '1px solid rgba(255,255,255,0.1)',
                            color: 'rgba(255, 255, 255, 0.9)',
                            fontSize: '0.95rem'
                          }}>
                            ✅ {feature}
                          </li>
                        ))}
                      </ul>
                      <button
                        onClick={tier.buttonAction}
                        style={{
                          width: '100%',
                          padding: '1.2rem',
                          background: tier.popular
                            ? 'linear-gradient(135deg, #ffffff, rgba(255,255,255,0.9))'
                            : 'rgba(255, 255, 255, 0.1)',
                          color: tier.popular ? '#667eea' : 'white',
                          border: tier.popular ? 'none' : '1px solid rgba(255, 255, 255, 0.3)',
                          borderRadius: '15px',
                          cursor: 'pointer',
                          fontWeight: '700',
                          fontSize: '1rem',
                          transition: 'all 0.3s ease',
                          textTransform: 'uppercase',
                          letterSpacing: '1px'
                        }}
                        onMouseOver={(e) => {
                          e.currentTarget.style.transform = 'translateY(-2px)';
                          e.currentTarget.style.boxShadow = '0 8px 25px rgba(255, 107, 53, 0.4)';
                        }}
                        onMouseOut={(e) => {
                          e.currentTarget.style.transform = 'translateY(0)';
                          e.currentTarget.style.boxShadow = 'none';
                        }}
                      >
                        {tier.buttonText}
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'about' && (
              <div>
                <h2 style={{ 
                  fontSize: '2.5rem', 
                  marginBottom: '2rem',
                  background: 'linear-gradient(45deg, #FFD700, #FFA500)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}>
                  About AgriIntel
                </h2>
                <div style={{
                  maxWidth: '800px',
                  margin: '0 auto',
                  ...cardStyle
                }}>
                  <p style={{ 
                    fontSize: '1.3rem', 
                    marginBottom: '2rem', 
                    opacity: 0.95,
                    lineHeight: 1.8
                  }}>
                    South Africa's leading livestock management platform, empowering farmers 
                    with AI-driven insights and comprehensive farm management solutions. 
                    Built specifically for South African conditions, supporting local currencies, 
                    languages, and agricultural practices.
                  </p>
                  <div style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
                    gap: '2rem',
                    marginTop: '3rem'
                  }}>
                    {stats.map((stat, index) => (
                      <div key={index} style={{ textAlign: 'center' }}>
                        <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>{stat.icon}</div>
                        <div style={{ 
                          fontSize: '1.8rem', 
                          fontWeight: 'bold', 
                          color: '#FFD700',
                          marginBottom: '0.5rem'
                        }}>
                          {stat.number}
                        </div>
                        <div style={{ opacity: 0.8 }}>{stat.label}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'contact' && (
              <div>
                <h2 style={{
                  fontSize: '2.5rem',
                  marginBottom: '3rem',
                  background: 'linear-gradient(45deg, #FFD700, #FFA500)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}>
                  Get in Touch
                </h2>
                <div style={{
                  maxWidth: '600px',
                  margin: '0 auto',
                  ...cardStyle
                }}>
                  <div style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '2rem'
                  }}>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '1rem',
                      padding: '1rem',
                      background: 'rgba(255,255,255,0.1)',
                      borderRadius: '12px'
                    }}>
                      <span style={{ fontSize: '2rem' }}>📞</span>
                      <div>
                        <div style={{ fontWeight: 'bold', color: '#FFD700' }}>Phone</div>
                        <div>+27 11 123 4567</div>
                      </div>
                    </div>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '1rem',
                      padding: '1rem',
                      background: 'rgba(255,255,255,0.1)',
                      borderRadius: '12px'
                    }}>
                      <span style={{ fontSize: '2rem' }}>📧</span>
                      <div>
                        <div style={{ fontWeight: 'bold', color: '#FFD700' }}>Email</div>
                        <div><EMAIL></div>
                      </div>
                    </div>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '1rem',
                      padding: '1rem',
                      background: 'rgba(255,255,255,0.1)',
                      borderRadius: '12px'
                    }}>
                      <span style={{ fontSize: '2rem' }}>📍</span>
                      <div>
                        <div style={{ fontWeight: 'bold', color: '#FFD700' }}>Location</div>
                        <div>Cape Town, South Africa</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'services' && (
              <div>
                <h2 style={{
                  fontSize: '2.5rem',
                  marginBottom: '3rem',
                  background: 'linear-gradient(45deg, #FFD700, #FFA500)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}>
                  Our Services
                </h2>
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',
                  gap: '2rem',
                  maxWidth: '1200px',
                  margin: '0 auto'
                }}>
                  {[
                    {
                      icon: '🐄',
                      title: 'Livestock Management',
                      description: 'Complete animal tracking, health monitoring, and breeding management for all livestock types.',
                      features: ['RFID Tracking', 'Health Records', 'Breeding Analytics', 'Performance Metrics']
                    },
                    {
                      icon: '💰',
                      title: 'Financial Analytics',
                      description: 'Comprehensive financial tracking and cost optimization for maximum farm profitability.',
                      features: ['Cost Analysis', 'Revenue Tracking', 'Budget Planning', 'ROI Reports']
                    },
                    {
                      icon: '🌱',
                      title: 'Feed Management',
                      description: 'Optimize feed costs and nutrition with intelligent feed planning and tracking.',
                      features: ['Feed Scheduling', 'Cost Optimization', 'Nutrition Analysis', 'Waste Reduction']
                    },
                    {
                      icon: '📱',
                      title: 'Mobile App - Coming Soon!',
                      description: 'Access AgriIntel on-the-go with our upcoming mobile application featuring real-time notifications.',
                      features: ['Real-time Alerts', 'Offline Access', 'Photo Capture', 'GPS Tracking'],
                      isComingSoon: true
                    }
                  ].map((service, index) => (
                    <div
                      key={index}
                      style={{
                        ...cardStyle,
                        background: service.isComingSoon
                          ? 'linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 193, 7, 0.1))'
                          : 'rgba(255, 255, 255, 0.1)',
                        border: service.isComingSoon
                          ? '2px solid rgba(255, 215, 0, 0.5)'
                          : '1px solid rgba(255, 255, 255, 0.2)',
                        position: 'relative',
                        cursor: 'pointer'
                      }}
                      onMouseOver={(e) => {
                        e.currentTarget.style.transform = 'translateY(-8px)';
                        e.currentTarget.style.background = service.isComingSoon
                          ? 'linear-gradient(135deg, rgba(255, 215, 0, 0.3), rgba(255, 193, 7, 0.2))'
                          : 'rgba(255,255,255,0.15)';
                      }}
                      onMouseOut={(e) => {
                        e.currentTarget.style.transform = 'translateY(0)';
                        e.currentTarget.style.background = service.isComingSoon
                          ? 'linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 193, 7, 0.1))'
                          : 'rgba(255, 255, 255, 0.1)';
                      }}
                    >
                      {service.isComingSoon && (
                        <div style={{
                          position: 'absolute',
                          top: '-10px',
                          right: '-10px',
                          background: 'linear-gradient(135deg, #FFD700, #FFA500)',
                          color: '#2E7D32',
                          padding: '0.5rem 1rem',
                          borderRadius: '20px',
                          fontSize: '0.8rem',
                          fontWeight: 'bold',
                          boxShadow: '0 4px 15px rgba(255, 215, 0, 0.4)',
                          animation: 'pulse 2s infinite'
                        }}>
                          COMING SOON!
                        </div>
                      )}
                      <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>{service.icon}</div>
                      <h3 style={{
                        marginBottom: '1rem',
                        fontSize: '1.3rem',
                        color: service.isComingSoon ? '#FFD700' : 'white'
                      }}>
                        {service.title}
                      </h3>
                      <p style={{
                        opacity: 0.9,
                        lineHeight: 1.6,
                        marginBottom: '1.5rem',
                        color: 'rgba(255, 255, 255, 0.9)'
                      }}>
                        {service.description}
                      </p>
                      <ul style={{
                        listStyle: 'none',
                        padding: 0,
                        textAlign: 'left'
                      }}>
                        {service.features.map((feature, i) => (
                          <li key={i} style={{
                            padding: '0.5rem 0',
                            color: 'rgba(255, 255, 255, 0.8)',
                            fontSize: '0.9rem',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.5rem'
                          }}>
                            <span style={{ color: '#FFD700' }}>✓</span>
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer style={{
        background: 'rgba(0,0,0,0.3)',
        padding: '3rem 2rem 2rem',
        textAlign: 'center',
        borderTop: '1px solid rgba(255,255,255,0.1)'
      }}>
        <div style={{ maxWidth: '1400px', margin: '0 auto' }}>
          <div style={{ marginBottom: '2rem' }}>
            <div style={{ 
              fontSize: '2rem', 
              fontWeight: 'bold', 
              marginBottom: '1rem',
              background: 'linear-gradient(45deg, #FFD700, #FFA500)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}>
              🌾 AgriIntel
            </div>
            <p style={{ 
              opacity: 0.9, 
              fontSize: '1.1rem',
              maxWidth: '600px',
              margin: '0 auto'
            }}>
              Smart Livestock Management for South African Farmers.<br />
              Empowering agriculture with AI technology and data-driven insights.
            </p>
          </div>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '2rem',
            marginBottom: '2rem'
          }}>
            <div>
              <h4 style={{ color: '#FFD700', marginBottom: '1rem' }}>Platform</h4>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                <button 
                  style={{ 
                    background: 'none', 
                    border: 'none', 
                    color: 'white', 
                    cursor: 'pointer',
                    opacity: 0.8,
                    fontSize: '1rem'
                  }}
                  onClick={() => navigate('/beta-login')}
                >
                  Beta Access
                </button>
                <button 
                  style={{ 
                    background: 'none', 
                    border: 'none', 
                    color: 'white', 
                    cursor: 'pointer',
                    opacity: 0.8,
                    fontSize: '1rem'
                  }}
                  onClick={() => navigate('/login')}
                >
                  Live Access
                </button>
              </div>
            </div>
            <div>
              <h4 style={{ color: '#FFD700', marginBottom: '1rem' }}>Support</h4>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                <span style={{ opacity: 0.8 }}>Documentation</span>
                <span style={{ opacity: 0.8 }}>Help Center</span>
                <span style={{ opacity: 0.8 }}>Training</span>
              </div>
            </div>
            <div>
              <h4 style={{ color: '#FFD700', marginBottom: '1rem' }}>Company</h4>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                <span style={{ opacity: 0.8 }}>About Us</span>
                <span style={{ opacity: 0.8 }}>Careers</span>
                <span style={{ opacity: 0.8 }}>Partners</span>
              </div>
            </div>
          </div>
          
          <div style={{ 
            borderTop: '1px solid rgba(255,255,255,0.1)',
            paddingTop: '2rem',
            opacity: 0.7
          }}>
            © 2024 AgriIntel. All rights reserved. Made with ❤️ for South African farmers.
          </div>
        </div>
      </footer>
    </div>
  );
};

export default FinalLandingPage;
