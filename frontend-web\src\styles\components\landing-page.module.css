/* Landing Page Component Styles - CSS Module */

/* <PERSON><PERSON> */
.buttonBase {
  padding: 1.2rem 2.5rem;
  margin: 0.5rem;
  border: none;
  border-radius: 50px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  text-transform: uppercase;
  letter-spacing: 1.5px;
  position: relative;
  overflow: hidden;
}

.primaryButton {
  composes: buttonBase;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.primaryButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.6);
}

.secondaryButton {
  composes: buttonBase;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.secondaryButton:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* Card Styles */
.card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 2.5rem;
  text-align: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.card:hover {
  transform: translateY(-8px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.card:hover::before {
  left: 100%;
}

/* Section Styles */
.sectionFull {
  width: 100%;
  padding: 6rem 4rem;
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .sectionFull {
    padding: 4rem 2rem;
  }
  
  .card {
    padding: 2rem;
  }
  
  .buttonBase {
    padding: 1rem 2rem;
    font-size: 0.9rem;
  }
}

/* Animation Classes */
.fadeInUp {
  animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slideInLeft {
  animation: slideInLeft 0.8s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slideInRight {
  animation: slideInRight 0.8s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Glassmorphism Effects */
.glassmorphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
}

.glassmorphismDark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
}

/* AgriIntel Theme Colors */
.primaryGradient {
  background: linear-gradient(135deg, #2e7d32 0%, #4caf50 25%, #8bc34a 50%, #66bb6a 75%, #43a047 100%);
}

.secondaryGradient {
  background: linear-gradient(135deg, #1565c0 0%, #1976d2 25%, #2196f3 50%, #42a5f5 75%, #1e88e5 100%);
}

.accentGradient {
  background: linear-gradient(135deg, #f57c00 0%, #ff9800 25%, #ffb74d 50%, #ffa726 75%, #fb8c00 100%);
}
