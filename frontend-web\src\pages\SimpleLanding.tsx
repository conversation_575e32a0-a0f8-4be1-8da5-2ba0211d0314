import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import '../styles/landing-components.css';
import '../styles/simple-landing.css';
import '../styles/enhanced-landing.css';

const SimpleLanding: React.FC = () => {
  const navigate = useNavigate();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Beautiful livestock images for rotating background
  const livestockImages = [
    '/images/animals/cattle-1.jpeg',
    '/images/animals/cattle-2.avif',
    '/images/animals/cattle-3.jpeg',
    '/images/animals/cattle-4.jpeg',
    '/images/animals/cattle-6.jpeg',
    '/images/animals/cows 3.jpeg',
    '/images/animals/cows 4.jpeg',
    '/images/animals/goat-1.jpeg',
    '/images/animals/goat-2.jpeg',
    '/images/animals/sheep-1.jpeg',
    '/images/animals/sheep-2.jpeg'
  ];

  // Rotate background images every 4 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prev) => (prev + 1) % livestockImages.length);
    }, 4000);
    return () => clearInterval(interval);
  }, [livestockImages.length]);

  const handleBetaClick = () => {
    navigate('/beta-login');
  };

  const handleLiveClick = () => {
    navigate('/login');
  };

  const handleRegisterClick = () => {
    navigate('/register');
  };

  return (
    <div className="enhanced-landing-container">
      {/* Dynamic Background with Livestock Images */}
      <div
        className="dynamic-background"
        style={{
          '--bg-image': `url(${livestockImages[currentImageIndex]})`
        } as React.CSSProperties}
      />

      {/* Header */}
      <header className="enhanced-landing-header">
        <h1 className="enhanced-landing-title">
          🌾 AgriIntel
          <span className="enhanced-beta-badge">
            BETA
          </span>
        </h1>
        <div className="enhanced-nav-buttons">
          <button
            type="button"
            onClick={handleBetaClick}
            className="enhanced-btn-beta"
          >
            🚀 BETA ACCESS
          </button>
          <button
            type="button"
            onClick={handleLiveClick}
            className="enhanced-btn-live"
          >
            ⚡ LIVE ACCESS
          </button>
        </div>
      </header>

      {/* Main Content */}
      <main className="enhanced-landing-main">
        <h2 className="enhanced-hero-title">
          🌾 Smart Livestock Management for South African Farmers
        </h2>

        <p className="enhanced-hero-subtitle">
          Join 15,000+ South African farmers who've transformed their operations with AI-powered livestock management.
          Track health, optimize feeding, and boost profits with AgriIntel.
        </p>

        {/* Action Buttons */}
        <div className="enhanced-action-buttons">
          <button
            type="button"
            onClick={handleRegisterClick}
            className="enhanced-cta-primary"
          >
            🚀 START FREE BETA
          </button>
          <button
            type="button"
            onClick={handleLiveClick}
            className="enhanced-cta-secondary"
          >
            ⚡ GO LIVE
          </button>
        </div>

        {/* Features Grid */}
        <div className="enhanced-features-grid">
          <div className="enhanced-feature-card">
            <div className="enhanced-feature-icon">🐄</div>
            <h3 className="enhanced-feature-title">Smart Livestock Tracking</h3>
            <p className="enhanced-feature-description">Real-time monitoring of cattle, sheep, and goats with RFID technology and AI-powered insights</p>
          </div>

          <div className="enhanced-feature-card">
            <div className="enhanced-feature-icon">🩺</div>
            <h3 className="enhanced-feature-title">AI Health Management</h3>
            <p className="enhanced-feature-description">Predictive health analytics to prevent diseases, reduce vet costs, and keep your livestock healthy</p>
          </div>

          <div className="enhanced-feature-card">
            <div className="enhanced-feature-icon">📊</div>
            <h3 className="enhanced-feature-title">Advanced Analytics</h3>
            <p className="enhanced-feature-description">Comprehensive insights, financial tracking, and reporting for smarter farm management decisions</p>
          </div>

          <div className="enhanced-feature-card">
            <div className="enhanced-feature-icon">🌾</div>
            <h3 className="enhanced-feature-title">Feed Management</h3>
            <p className="enhanced-feature-description">Optimize nutrition, track feed costs, and automate feeding schedules for maximum efficiency</p>
          </div>

          <div className="enhanced-feature-card">
            <div className="enhanced-feature-icon">🛡️</div>
            <h3 className="enhanced-feature-title">Security & Safety</h3>
            <p className="enhanced-feature-description">Emergency response system with panic button and theft prevention for farm security</p>
          </div>

          <div className="enhanced-feature-card">
            <div className="enhanced-feature-icon">🏪</div>
            <h3 className="enhanced-feature-title">Service Marketplace</h3>
            <p className="enhanced-feature-description">Connect with veterinarians, suppliers, auctioneers, and security services in your area</p>
          </div>
        </div>

        {/* Stats */}
        <div className="enhanced-stats-grid">
          <div className="enhanced-stat-item">
            <div className="enhanced-stat-number">15K+</div>
            <div className="enhanced-stat-label">South African Farmers</div>
          </div>
          <div className="enhanced-stat-item">
            <div className="enhanced-stat-number">750K+</div>
            <div className="enhanced-stat-label">Livestock Tracked</div>
          </div>
          <div className="enhanced-stat-item">
            <div className="enhanced-stat-number">99.9%</div>
            <div className="enhanced-stat-label">System Uptime</div>
          </div>
          <div className="enhanced-stat-item">
            <div className="enhanced-stat-number">40%</div>
            <div className="enhanced-stat-label">Cost Reduction</div>
          </div>
        </div>

        {/* Pricing */}
        <div className="enhanced-pricing-grid">
          <div className="enhanced-pricing-card">
            <h3 className="enhanced-pricing-title">Beta Access</h3>
            <div className="enhanced-pricing-price">FREE</div>
            <p className="enhanced-pricing-description">Perfect for small-scale farmers</p>
            <ul className="enhanced-pricing-features">
              <li>✅ Up to 50 animals</li>
              <li>✅ Basic health monitoring</li>
              <li>✅ Mobile app access</li>
            </ul>
            <button
              type="button"
              onClick={handleBetaClick}
              className="enhanced-pricing-button beta"
            >
              START BETA
            </button>
          </div>

          <div className="enhanced-pricing-card featured">
            <h3 className="enhanced-pricing-title">Professional</h3>
            <div className="enhanced-pricing-price">R299</div>
            <p className="enhanced-pricing-description">For growing commercial farms</p>
            <ul className="enhanced-pricing-features">
              <li>✅ Up to 500 animals</li>
              <li>✅ AI health analytics</li>
              <li>✅ Financial management</li>
              <li>✅ Priority support</li>
            </ul>
            <button
              type="button"
              onClick={handleLiveClick}
              className="enhanced-pricing-button live"
            >
              GO LIVE
            </button>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="enhanced-landing-footer">
        <h3 className="enhanced-footer-title">🌾 AgriIntel</h3>
        <p className="enhanced-footer-description">Smart Livestock Management for South African Farmers</p>
        <div className="enhanced-footer-contact">
          <span>📞 +27 11 123 4567</span>
          <span>📧 <EMAIL></span>
          <span>📍 Cape Town, South Africa</span>
        </div>
        <p className="enhanced-footer-copyright">
          © 2024 AgriIntel. All rights reserved. Made with ❤️ by May and Caiphus for South African farmers.
        </p>
      </footer>
    </div>
  );
};

export default SimpleLanding;
