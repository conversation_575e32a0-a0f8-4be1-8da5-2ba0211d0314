/* Professional Landing Page Styles */

/* Accessibility - Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.professional-landing {
  min-height: 100vh;
  background: linear-gradient(135deg, rgba(46, 125, 50, 0.85) 0%, rgba(27, 94, 32, 0.9) 50%, rgba(76, 175, 80, 0.8) 100%);
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  color: white;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  position: relative;
}

.professional-landing::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(76, 175, 80, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(27, 94, 32, 0.3) 0%, transparent 50%);
  pointer-events: none;
  z-index: 1;
}

.professional-landing > * {
  position: relative;
  z-index: 2;
}

/* Navigation Styles */
.landing-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(0, 0, 0, 0.3);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.nav-brand h1 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: bold;
}

.brand-tagline {
  font-size: 0.9rem;
  opacity: 0.8;
  margin-left: 0.5rem;
}

.nav-tabs {
  display: flex;
  gap: 1rem;
}

.nav-tab {
  background: transparent;
  border: none;
  color: white;
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.nav-tab:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

.nav-tab.active {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  font-weight: 600;
}

.nav-actions {
  display: flex;
  gap: 1rem;
}

.nav-button {
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.nav-button.beta {
  background: #FF9800;
  color: white;
}

.nav-button.live {
  background: #2196F3;
  color: white;
}

.nav-button.pro {
  background: #9C27B0;
  color: white;
}

.nav-button.register {
  background: #4CAF50;
  color: white;
  border: 2px solid #66BB6A;
}

.nav-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

/* Main Content */
.landing-main {
  min-height: calc(100vh - 80px);
  padding: 2rem;
}

.tab-content {
  max-width: 1200px;
  margin: 0 auto;
}

/* Hero Section */
.hero-section {
  text-align: center;
  padding: 4rem 0;
  background: linear-gradient(135deg, rgba(46, 125, 50, 0.7) 0%, rgba(27, 94, 32, 0.8) 100%);
  background-size: cover;
  background-position: center;
  border-radius: 20px;
  margin: 2rem 0;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 30% 40%, rgba(255, 215, 0, 0.1) 0%, transparent 60%),
    radial-gradient(circle at 70% 60%, rgba(76, 175, 80, 0.2) 0%, transparent 60%);
  pointer-events: none;
  z-index: 1;
}

.hero-section > * {
  position: relative;
  z-index: 2;
}

.hero-content {
  margin-bottom: 4rem;
}

.hero-title {
  font-size: 4rem;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  background: linear-gradient(45deg, #FFD700, #FFA000);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.8rem;
  margin-bottom: 1rem;
  opacity: 0.9;
}

.hero-description {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.8;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.hero-buttons {
  display: flex;
  gap: 2rem;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-button {
  padding: 1.5rem 3rem;
  font-size: 1.3rem;
  font-weight: bold;
  border: none;
  border-radius: 50px;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.cta-button.beta {
  background: linear-gradient(45deg, #FF9800, #F57C00);
  color: white;
}

.cta-button.live {
  background: linear-gradient(45deg, #2196F3, #1976D2);
  color: white;
}

.cta-button.register {
  background: linear-gradient(45deg, #4CAF50, #2E7D32);
  color: white;
  border: 2px solid #66BB6A;
}

.cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Hero Stats */
.hero-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-top: 4rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: bold;
  color: #FFD700;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 1rem;
  opacity: 0.9;
}

/* About Section */
.about-section {
  padding: 2rem 0;
}

.about-section h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: #FFD700;
}

.about-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.about-card {
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
}

.about-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
}

.about-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.about-card h3 {
  color: #FFD700;
  margin-bottom: 1rem;
}

.company-stats {
  text-align: center;
  margin-top: 4rem;
}

.company-stats h3 {
  font-size: 2rem;
  margin-bottom: 2rem;
  color: #FFD700;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
}

.company-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
}

.company-stat strong {
  font-size: 1.5rem;
  color: #FFD700;
  margin-bottom: 0.5rem;
}

/* Services Section */
.services-section {
  padding: 2rem 0;
}

.services-section h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: #FFD700;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.service-card {
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(27, 94, 32, 0.1) 100%);
  opacity: 0.1;
  transition: opacity 0.3s ease;
  z-index: 0;
}

.service-card:nth-child(1)::before {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.2) 0%, rgba(46, 125, 50, 0.2) 100%);
}

.service-card:nth-child(2)::before {
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.2) 0%, rgba(25, 118, 210, 0.2) 100%);
}

.service-card:nth-child(3)::before {
  background: linear-gradient(135deg, rgba(233, 30, 99, 0.2) 0%, rgba(194, 24, 91, 0.2) 100%);
}

.service-card:nth-child(4)::before {
  background: linear-gradient(135deg, rgba(255, 152, 0, 0.2) 0%, rgba(245, 124, 0, 0.2) 100%);
}

.service-card:nth-child(5)::before {
  background: linear-gradient(135deg, rgba(156, 39, 176, 0.2) 0%, rgba(123, 31, 162, 0.2) 100%);
}

.service-card:nth-child(6)::before {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.2) 0%, rgba(255, 160, 0, 0.2) 100%);
}

.service-card > * {
  position: relative;
  z-index: 1;
}

.service-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.service-card:hover::before {
  opacity: 0.2;
}

.service-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.service-card h3 {
  color: #FFD700;
  margin-bottom: 1rem;
}

.service-card ul {
  list-style: none;
  padding: 0;
  margin-top: 1rem;
}

.service-card li {
  padding: 0.3rem 0;
  opacity: 0.9;
}

/* Pricing Section */
.pricing-section {
  padding: 2rem 0;
  text-align: center;
}

.pricing-section h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #FFD700;
}

.pricing-subtitle {
  font-size: 1.2rem;
  opacity: 0.8;
  margin-bottom: 3rem;
}

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1000px;
  margin: 0 auto;
}

.pricing-card {
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
  position: relative;
  transition: all 0.3s ease;
}

.pricing-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
}

.pricing-card.featured {
  border: 2px solid #FFD700;
  transform: scale(1.05);
}

.pricing-badge {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  background: #FFD700;
  color: #1B5E20;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: bold;
}

.pricing-header h3 {
  color: #FFD700;
  margin-bottom: 1rem;
}

.price {
  font-size: 2.5rem;
  font-weight: bold;
  color: #FFD700;
  margin-bottom: 0.5rem;
}

.price span {
  font-size: 1rem;
  opacity: 0.7;
}

.pricing-features ul {
  list-style: none;
  padding: 0;
  margin: 2rem 0;
  text-align: left;
}

.pricing-features li {
  padding: 0.5rem 0;
  opacity: 0.9;
}

.pricing-button {
  width: 100%;
  padding: 1rem 2rem;
  border: none;
  border-radius: 25px;
  font-size: 1.1rem;
  font-weight: bold;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.pricing-button.beta {
  background: linear-gradient(45deg, #FF9800, #F57C00);
  color: white;
}

.pricing-button.professional {
  background: linear-gradient(45deg, #2196F3, #1976D2);
  color: white;
}

.pricing-button.enterprise {
  background: linear-gradient(45deg, #9C27B0, #7B1FA2);
  color: white;
}

.pricing-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

/* Contact Section */
.contact-section {
  padding: 2rem 0;
}

.contact-section h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: #FFD700;
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  max-width: 1000px;
  margin: 0 auto;
}

.contact-info h3 {
  color: #FFD700;
  margin-bottom: 2rem;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 2rem;
}

.contact-icon {
  font-size: 1.5rem;
  margin-top: 0.2rem;
}

.contact-item strong {
  color: #FFD700;
  display: block;
  margin-bottom: 0.5rem;
}

.contact-form {
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
}

.contact-form h3 {
  color: #FFD700;
  margin-bottom: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1rem;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.form-group select {
  color: white;
}

.form-group select option {
  background: #1B5E20;
  color: white;
}

.contact-submit {
  width: 100%;
  padding: 1rem 2rem;
  background: linear-gradient(45deg, #2196F3, #1976D2);
  color: white;
  border: none;
  border-radius: 25px;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.contact-submit:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

/* Footer */
.landing-footer {
  background: rgba(0, 0, 0, 0.4);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 3rem 2rem 1rem;
  margin-top: 4rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.footer-section h3,
.footer-section h4 {
  color: #FFD700;
  margin-bottom: 1rem;
}

.footer-section p {
  opacity: 0.8;
  margin-bottom: 0.5rem;
}

.footer-social {
  margin-top: 1rem;
}

.footer-social span {
  margin-right: 1rem;
  opacity: 0.8;
}

.footer-social a {
  color: #FFD700;
  text-decoration: none;
  margin-right: 1rem;
  transition: opacity 0.3s ease;
}

.footer-social a:hover {
  opacity: 0.7;
}

.footer-section ul {
  list-style: none;
  padding: 0;
}

.footer-section li {
  margin-bottom: 0.5rem;
}

.footer-section button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.footer-section button:hover {
  opacity: 1;
  color: #FFD700;
}

.footer-bottom {
  text-align: center;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  opacity: 0.7;
}

/* Responsive Design */
@media (max-width: 768px) {
  .landing-nav {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .nav-tabs {
    flex-wrap: wrap;
    justify-content: center;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .contact-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .pricing-card.featured {
    transform: none;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }
}
