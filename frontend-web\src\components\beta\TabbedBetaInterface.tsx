import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Tabs,
  Tab,
  Grid,
  Card,
  CardContent,
  useTheme,
  alpha,
  Chip,
  Button
} from '@mui/material';
import {
  Dashboard,
  Pets,
  LocalHospital,
  TrendingUp,
  AccountBalance,
  Inventory,
  Assessment,
  Settings,
  Visibility,
  Star,
  Upgrade
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import ModernConnectedCard from '../common/ModernConnectedCard';
import { hasModuleAccess, moduleAccessConfig } from '../../utils/betaAccessControl';
import { useAuth } from '../../contexts/AuthContext';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`beta-tabpanel-${index}`}
      aria-labelledby={`beta-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `beta-tab-${index}`,
    'aria-controls': `beta-tabpanel-${index}`,
  };
}

interface TabbedBetaInterfaceProps {
  user: any;
  onUpgrade: () => void;
  onModuleClick: (module: any) => void;
}

const TabbedBetaInterface: React.FC<TabbedBetaInterfaceProps> = ({
  user,
  onUpgrade,
  onModuleClick
}) => {
  const [value, setValue] = useState(0);
  const theme = useTheme();
  const navigate = useNavigate();
  const { user: authUser } = useAuth();

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  // Enhanced livestock statistics
  const livestockStats = {
    totalAnimals: 247,
    healthyAnimals: 231,
    pregnantAnimals: 18,
    newBorns: 12,
    avgWeight: 485,
    milkProduction: 1250,
    feedConsumption: 2840,
    vaccinations: 45,
    breeds: ['Holstein', 'Angus', 'Hereford', 'Simmental'],
    recentActivities: [
      { type: 'birth', animal: 'Cow #247', time: '2 hours ago', icon: '🐄' },
      { type: 'vaccination', animal: 'Bull #023', time: '4 hours ago', icon: '💉' },
      { type: 'health_check', animal: 'Heifer #156', time: '6 hours ago', icon: '🩺' },
      { type: 'feeding', animal: 'Herd A', time: '8 hours ago', icon: '🌾' }
    ],
    alerts: [
      { type: 'health', message: 'Cow #134 requires veterinary attention', priority: 'high', icon: '⚠️' },
      { type: 'feeding', message: 'Feed inventory running low', priority: 'medium', icon: '📦' },
      { type: 'breeding', message: '3 animals ready for breeding', priority: 'low', icon: '💝' }
    ]
  };

  // Beta modules for different tabs
  const betaModules = [
    {
      id: 'dashboard',
      name: 'Dashboard',
      icon: <Dashboard />,
      description: `${livestockStats.totalAnimals} Animals • ${livestockStats.healthyAnimals} Healthy`,
      isLocked: !hasModuleAccess(authUser, 'dashboard'),
      isPremium: moduleAccessConfig.dashboard.isPremiumFeature,
      color: '#2196F3',
      route: '/dashboard',
      backgroundImage: '/images/dashboard/main-dashboard.jpg'
    },
    {
      id: 'animals',
      name: 'Animal Management',
      icon: <Pets />,
      description: `${livestockStats.totalAnimals} Total • Beta: 50 max`,
      isLocked: !hasModuleAccess(authUser, 'animals'),
      isPremium: moduleAccessConfig.animals.isPremiumFeature,
      color: '#4CAF50',
      route: '/dashboard/animals',
      backgroundImage: '/images/modules/animals/cattle-2.avif'
    },
    {
      id: 'health',
      name: 'Health Monitoring',
      icon: <LocalHospital />,
      description: 'Basic health tracking (Beta Limited)',
      isLocked: !hasModuleAccess(authUser, 'health'),
      isPremium: moduleAccessConfig.health.isPremiumFeature,
      color: '#F44336',
      route: '/dashboard/health',
      backgroundImage: '/images/modules/health/veterinary-1.jpg'
    },
    {
      id: 'resources',
      name: 'Resources & Information',
      icon: <Visibility />,
      description: 'Government resources, auctions, information',
      isLocked: !hasModuleAccess(authUser, 'resources'),
      isPremium: moduleAccessConfig.resources.isPremiumFeature,
      color: '#00BCD4',
      route: '/dashboard/resources',
      backgroundImage: '/images/modules/resources/resources-1.jpg'
    }
  ];

  const premiumModules = [
    {
      id: 'breeding',
      name: 'Breeding Management',
      icon: <TrendingUp />,
      description: 'Advanced breeding analytics & records',
      isLocked: !hasModuleAccess(authUser, 'breeding'),
      isPremium: true,
      color: '#9C27B0',
      backgroundImage: '/images/modules/animals/cattle-3.jpeg'
    },
    {
      id: 'financial',
      name: 'Financial Management',
      icon: <AccountBalance />,
      description: 'Complete financial tracking & reports',
      isLocked: !hasModuleAccess(authUser, 'financial'),
      isPremium: true,
      color: '#FF9800',
      backgroundImage: '/images/modules/commercial/commercial-1.jpeg'
    },
    {
      id: 'inventory',
      name: 'Inventory Management',
      icon: <Inventory />,
      description: 'Smart inventory with auto-reordering',
      isLocked: !hasModuleAccess(authUser, 'inventory'),
      isPremium: true,
      color: '#607D8B',
      backgroundImage: '/images/modules/feeding/feed-main.jpeg'
    },
    {
      id: 'analytics',
      name: 'AI Analytics',
      icon: <Assessment />,
      description: 'Predictive insights & recommendations',
      isLocked: !hasModuleAccess(authUser, 'analytics'),
      isPremium: true,
      color: '#E91E63',
      backgroundImage: '/images/modules/rfid/rfid-3.jpg'
    }
  ];

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ textAlign: 'center', mb: 4 }}>
        <Typography
          variant="h3"
          sx={{
            fontWeight: 'bold',
            color: 'white',
            mb: 2,
            textShadow: '0 2px 4px rgba(0,0,0,0.3)'
          }}
        >
          🌾 AgriIntel BETA Dashboard
        </Typography>
        <Chip 
          label="BETA ACCESS" 
          color="secondary" 
          size="medium" 
          sx={{ 
            fontWeight: 'bold',
            background: 'linear-gradient(45deg, #FF6B35, #F7931E)',
            color: 'white',
            fontSize: '1rem',
            px: 2
          }} 
        />
      </Box>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs 
          value={value} 
          onChange={handleChange} 
          centered
          sx={{
            '& .MuiTab-root': {
              color: alpha('#fff', 0.7),
              fontWeight: 600,
              fontSize: '1rem',
              minWidth: 120,
              '&.Mui-selected': {
                color: '#fff',
              }
            },
            '& .MuiTabs-indicator': {
              backgroundColor: '#4CAF50',
              height: 3,
              borderRadius: 2
            }
          }}
        >
          <Tab label="Overview" {...a11yProps(0)} />
          <Tab label="Beta Modules" {...a11yProps(1)} />
          <Tab label="Premium Features" {...a11yProps(2)} />
          <Tab label="Statistics" {...a11yProps(3)} />
        </Tabs>
      </Box>

      {/* Tab Panels */}
      <AnimatePresence mode="wait">
        <motion.div
          key={value}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
        >
          {/* Overview Tab */}
          <TabPanel value={value} index={0}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={8}>
                <Card sx={{
                  background: alpha(theme.palette.background.paper, 0.9),
                  backdropFilter: 'blur(20px)',
                  borderRadius: 3,
                  p: 3
                }}>
                  <Typography variant="h5" sx={{ mb: 2, fontWeight: 'bold' }}>
                    Welcome to AgriIntel Beta! 🎉
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2, lineHeight: 1.6 }}>
                    You're experiencing the future of intelligent livestock management. 
                    This beta version gives you access to essential features with some limitations.
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                    <Chip label={`${user.animalsCount}/${user.maxAnimals} Animals`} color="primary" />
                    <Chip label={`${user.trialDaysLeft} Days Left`} color="warning" />
                    <Chip label="Beta Access" color="secondary" />
                  </Box>
                </Card>
              </Grid>
              <Grid item xs={12} md={4}>
                <Card sx={{
                  background: 'linear-gradient(135deg, #4CAF50, #66BB6A)',
                  color: 'white',
                  borderRadius: 3,
                  p: 3,
                  textAlign: 'center'
                }}>
                  <Typography variant="h6" sx={{ mb: 1, fontWeight: 'bold' }}>
                    Ready to Upgrade?
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2, opacity: 0.9 }}>
                    Unlock unlimited animals and premium features
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<Upgrade />}
                    onClick={onUpgrade}
                    sx={{
                      background: 'rgba(255,255,255,0.2)',
                      '&:hover': {
                        background: 'rgba(255,255,255,0.3)'
                      }
                    }}
                  >
                    Upgrade Now
                  </Button>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>

          {/* Beta Modules Tab */}
          <TabPanel value={value} index={1}>
            <Typography variant="h5" sx={{ mb: 3, color: 'white', fontWeight: 'bold' }}>
              Available Beta Modules
            </Typography>
            <Grid container spacing={3}>
              {betaModules.map((module, index) => (
                <Grid item xs={12} sm={6} md={3} key={module.id}>
                  <ModernConnectedCard
                    id={module.id}
                    name={module.name}
                    description={module.description}
                    icon={module.icon}
                    backgroundImage={module.backgroundImage}
                    color={module.color}
                    isLocked={module.isLocked}
                    isPremium={module.isPremium}
                    onClick={() => onModuleClick(module)}
                    index={index}
                    size="medium"
                  />
                </Grid>
              ))}
            </Grid>
          </TabPanel>

          {/* Premium Features Tab */}
          <TabPanel value={value} index={2}>
            <Typography variant="h5" sx={{ mb: 3, color: 'white', fontWeight: 'bold' }}>
              Premium Features (Upgrade Required)
            </Typography>
            <Grid container spacing={3}>
              {premiumModules.map((module, index) => (
                <Grid item xs={12} sm={6} md={3} key={module.id}>
                  <ModernConnectedCard
                    id={module.id}
                    name={module.name}
                    description={module.description}
                    icon={module.icon}
                    backgroundImage={module.backgroundImage}
                    color={module.color}
                    isLocked={module.isLocked}
                    isPremium={module.isPremium}
                    onClick={() => onModuleClick(module)}
                    index={index}
                    size="medium"
                  />
                </Grid>
              ))}
            </Grid>
          </TabPanel>

          {/* Statistics Tab */}
          <TabPanel value={value} index={3}>
            <Typography variant="h5" sx={{ mb: 3, color: 'white', fontWeight: 'bold' }}>
              Live Farm Statistics
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{
                  background: 'linear-gradient(135deg, #2196F3, #42A5F5)',
                  color: 'white',
                  borderRadius: 3,
                  p: 3,
                  textAlign: 'center'
                }}>
                  <Typography variant="h2" sx={{ fontWeight: 'bold', mb: 1 }}>
                    {livestockStats.totalAnimals}
                  </Typography>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>Total Animals</Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9, mt: 1 }}>
                    {livestockStats.healthyAnimals} Healthy ({Math.round((livestockStats.healthyAnimals/livestockStats.totalAnimals)*100)}%)
                  </Typography>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{
                  background: 'linear-gradient(135deg, #4CAF50, #66BB6A)',
                  color: 'white',
                  borderRadius: 3,
                  p: 3,
                  textAlign: 'center'
                }}>
                  <Typography variant="h2" sx={{ fontWeight: 'bold', mb: 1 }}>
                    {livestockStats.milkProduction}L
                  </Typography>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>Daily Milk</Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9, mt: 1 }}>
                    Target: 1400L ({Math.round((livestockStats.milkProduction/1400)*100)}%)
                  </Typography>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{
                  background: 'linear-gradient(135deg, #FF9800, #FFB74D)',
                  color: 'white',
                  borderRadius: 3,
                  p: 3,
                  textAlign: 'center'
                }}>
                  <Typography variant="h2" sx={{ fontWeight: 'bold', mb: 1 }}>
                    {livestockStats.pregnantAnimals}
                  </Typography>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>Pregnant</Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9, mt: 1 }}>
                    {livestockStats.newBorns} births this month
                  </Typography>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{
                  background: 'linear-gradient(135deg, #9C27B0, #BA68C8)',
                  color: 'white',
                  borderRadius: 3,
                  p: 3,
                  textAlign: 'center'
                }}>
                  <Typography variant="h2" sx={{ fontWeight: 'bold', mb: 1 }}>
                    {livestockStats.avgWeight}kg
                  </Typography>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>Avg Weight</Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9, mt: 1 }}>
                    +1.2kg/day growth
                  </Typography>
                </Card>
              </Grid>
            </Grid>

            {/* Recent Activities & Alerts */}
            <Grid container spacing={3} sx={{ mt: 2 }}>
              <Grid item xs={12} md={6}>
                <Card sx={{
                  background: alpha(theme.palette.background.paper, 0.9),
                  backdropFilter: 'blur(20px)',
                  borderRadius: 3,
                  p: 3
                }}>
                  <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
                    📊 Recent Activities
                  </Typography>
                  {livestockStats.recentActivities.map((activity, index) => (
                    <Box key={index} sx={{
                      display: 'flex',
                      alignItems: 'center',
                      mb: 1.5,
                      p: 1,
                      borderRadius: 2,
                      background: alpha(theme.palette.primary.main, 0.05)
                    }}>
                      <Typography sx={{ fontSize: '1.2rem', mr: 2 }}>
                        {activity.icon}
                      </Typography>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                          {activity.animal}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {activity.type.replace('_', ' ')} • {activity.time}
                        </Typography>
                      </Box>
                    </Box>
                  ))}
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card sx={{
                  background: alpha(theme.palette.background.paper, 0.9),
                  backdropFilter: 'blur(20px)',
                  borderRadius: 3,
                  p: 3
                }}>
                  <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
                    🚨 Active Alerts
                  </Typography>
                  {livestockStats.alerts.map((alert, index) => (
                    <Box key={index} sx={{
                      display: 'flex',
                      alignItems: 'center',
                      mb: 1.5,
                      p: 1,
                      borderRadius: 2,
                      background: alert.priority === 'high' ? alpha('#f44336', 0.1) :
                                 alert.priority === 'medium' ? alpha('#ff9800', 0.1) :
                                 alpha('#4caf50', 0.1)
                    }}>
                      <Typography sx={{ fontSize: '1.2rem', mr: 2 }}>
                        {alert.icon}
                      </Typography>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                          {alert.message}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Priority: {alert.priority}
                        </Typography>
                      </Box>
                    </Box>
                  ))}
                </Card>
              </Grid>
            </Grid>
          </TabPanel>
        </motion.div>
      </AnimatePresence>
    </Container>
  );
};

export default TabbedBetaInterface;
