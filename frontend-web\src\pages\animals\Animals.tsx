import React from 'react';
import { Routes, Route, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Box, CircularProgress } from '@mui/material';
import { useAuth } from '../../contexts/AuthContext';

import AnimalsDashboard from './AnimalsDashboard';
import AnimalDetail from './AnimalDetail';
import AnimalForm from './AnimalForm';
import AnimalsList from './AnimalsList';
import Profiles from './Profiles';
import Records from './Records';
import Tracking from './Tracking';
import Genealogy from './Genealogy';
import RFID from './RFID';
import AssetManagement from './AssetManagement';
import AssetManagementDashboard from './AssetManagementDashboard';
import RetirementTracking from './RetirementTracking';
import { useAnimalData } from '../../hooks/useAnimalData';
import BetaAnimalsModule from '../../components/beta/BetaAnimalsModule';

const Animals = () => {
  const { user } = useAuth();
  const { loading } = useAnimalData();

  // Check if user is BETA user
  const isBetaUser = user?.role === 'beta';

  // If BETA user, render BETA Animals Module
  if (isBetaUser) {
    return <BetaAnimalsModule />;
  }

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" my={4}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <Routes>
        <Route path="/" element={<AnimalsDashboard />} />
        <Route path="/list" element={<AnimalsList />} />
        <Route path="/new" element={<AnimalForm />} />
        <Route path="/edit/:id" element={<AnimalForm />} />
        <Route path="/:id" element={<AnimalDetail />} />
        <Route path="/profiles" element={<Profiles />} />
        <Route path="/records" element={<Records />} />
        <Route path="/tracking" element={<Tracking />} />
        <Route path="/genealogy" element={<Genealogy />} />
        <Route path="/rfid" element={<RFID />} />
        <Route path="/asset-management/*" element={<AssetManagement />} />
      </Routes>
    </motion.div>
  );
};

export default Animals; // Add default export



