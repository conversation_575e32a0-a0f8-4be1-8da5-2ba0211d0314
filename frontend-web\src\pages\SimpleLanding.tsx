import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import '../styles/landing-components.css';
import '../styles/simple-landing.css';

const SimpleLanding: React.FC = () => {
  const navigate = useNavigate();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Beautiful livestock images for rotating background
  const livestockImages = [
    '/images/animals/cattle-1.jpeg',
    '/images/animals/cattle-2.avif',
    '/images/animals/cattle-3.jpeg',
    '/images/animals/cattle-4.jpeg',
    '/images/animals/cattle-6.jpeg',
    '/images/animals/cows 3.jpeg',
    '/images/animals/cows 4.jpeg',
    '/images/animals/goat-1.jpeg',
    '/images/animals/goat-2.jpeg',
    '/images/animals/sheep-1.jpeg',
    '/images/animals/sheep-2.jpeg'
  ];

  // Rotate background images every 4 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prev) => (prev + 1) % livestockImages.length);
    }, 4000);
    return () => clearInterval(interval);
  }, [livestockImages.length]);

  const handleBetaClick = () => {
    navigate('/beta-login');
  };

  const handleLiveClick = () => {
    navigate('/login');
  };

  const handleRegisterClick = () => {
    navigate('/register');
  };

  return (
    <div className="basic-landing-container" style={{ position: 'relative', minHeight: '100vh' }}>
      {/* Dynamic Background with Livestock Images */}
      <div
        className="dynamic-background"
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          backgroundImage: `linear-gradient(135deg, rgba(46, 125, 50, 0.85), rgba(76, 175, 80, 0.7)), url(${livestockImages[currentImageIndex]})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          transition: 'background-image 1.5s ease-in-out',
          zIndex: -1
        }}
      />

      {/* Header */}
      <header className="basic-landing-header" style={{
        background: 'rgba(46, 125, 50, 0.95)',
        backdropFilter: 'blur(10px)',
        borderBottom: '1px solid rgba(255,255,255,0.1)',
        position: 'sticky',
        top: 0,
        zIndex: 100
      }}>
        <h1 className="basic-landing-title" style={{
          color: 'white',
          textShadow: '2px 2px 4px rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          gap: '10px'
        }}>
          🌾 AgriIntel
          <span style={{
            background: '#FFD700',
            color: '#2E7D32',
            padding: '4px 12px',
            borderRadius: '20px',
            fontSize: '0.6em',
            fontWeight: 'bold'
          }}>
            BETA
          </span>
        </h1>
        <div>
          <button
            type="button"
            onClick={handleBetaClick}
            className="landing-button beta"
            style={{
              background: '#FFD700',
              color: '#2E7D32',
              border: 'none',
              padding: '12px 24px',
              borderRadius: '25px',
              fontWeight: 'bold',
              marginRight: '10px',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              boxShadow: '0 4px 15px rgba(255, 215, 0, 0.3)'
            }}
          >
            🚀 BETA ACCESS
          </button>
          <button
            type="button"
            onClick={handleLiveClick}
            className="landing-button live"
            style={{
              background: 'transparent',
              color: 'white',
              border: '2px solid white',
              padding: '10px 24px',
              borderRadius: '25px',
              fontWeight: 'bold',
              cursor: 'pointer',
              transition: 'all 0.3s ease'
            }}
          >
            ⚡ LIVE ACCESS
          </button>
        </div>
      </header>

      {/* Main Content */}
      <main className="basic-landing-main" style={{
        padding: '80px 20px 40px',
        textAlign: 'center',
        position: 'relative',
        zIndex: 1
      }}>
        <h2 className="basic-landing-hero-title" style={{
          fontSize: 'clamp(2.5rem, 5vw, 4.5rem)',
          fontWeight: 'bold',
          color: 'white',
          textShadow: '3px 3px 6px rgba(0,0,0,0.7)',
          marginBottom: '20px',
          lineHeight: '1.2'
        }}>
          🌾 Smart Livestock Management for South African Farmers
        </h2>

        <p className="basic-landing-hero-subtitle" style={{
          fontSize: 'clamp(1.2rem, 3vw, 1.8rem)',
          color: 'rgba(255,255,255,0.95)',
          textShadow: '2px 2px 4px rgba(0,0,0,0.6)',
          marginBottom: '40px',
          maxWidth: '800px',
          margin: '0 auto 40px'
        }}>
          Join 15,000+ South African farmers who've transformed their operations with AI-powered livestock management.
          Track health, optimize feeding, and boost profits with AgriIntel.
        </p>

        {/* Action Buttons */}
        <div className="basic-landing-action-buttons" style={{
          display: 'flex',
          gap: '20px',
          justifyContent: 'center',
          flexWrap: 'wrap',
          marginBottom: '60px'
        }}>
          <button
            type="button"
            onClick={handleRegisterClick}
            className="landing-cta-button-large beta"
            style={{
              background: 'linear-gradient(135deg, #FFD700, #FFC107)',
              color: '#2E7D32',
              border: 'none',
              padding: '20px 40px',
              borderRadius: '50px',
              fontSize: '1.3rem',
              fontWeight: 'bold',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              boxShadow: '0 8px 25px rgba(255, 215, 0, 0.4)',
              transform: 'scale(1)',
              minWidth: '250px'
            }}
            onMouseOver={(e) => {
              e.currentTarget.style.transform = 'scale(1.05)';
              e.currentTarget.style.boxShadow = '0 12px 35px rgba(255, 215, 0, 0.6)';
            }}
            onMouseOut={(e) => {
              e.currentTarget.style.transform = 'scale(1)';
              e.currentTarget.style.boxShadow = '0 8px 25px rgba(255, 215, 0, 0.4)';
            }}
          >
            🚀 START FREE BETA
          </button>
          <button
            type="button"
            onClick={handleLiveClick}
            className="landing-cta-button-large live-outline"
            style={{
              background: 'rgba(255,255,255,0.1)',
              color: 'white',
              border: '3px solid white',
              padding: '17px 40px',
              borderRadius: '50px',
              fontSize: '1.2rem',
              fontWeight: 'bold',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              backdropFilter: 'blur(10px)',
              minWidth: '200px'
            }}
            onMouseOver={(e) => {
              e.currentTarget.style.background = 'rgba(255,255,255,0.2)';
              e.currentTarget.style.transform = 'translateY(-2px)';
            }}
            onMouseOut={(e) => {
              e.currentTarget.style.background = 'rgba(255,255,255,0.1)';
              e.currentTarget.style.transform = 'translateY(0)';
            }}
          >
            ⚡ GO LIVE
          </button>
        </div>

        {/* Features Grid */}
        <div className="simple-landing-features-grid" style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
          gap: '30px',
          marginBottom: '60px',
          maxWidth: '1200px',
          margin: '0 auto 60px'
        }}>
          <div className="simple-landing-feature-card" style={{
            background: 'rgba(255,255,255,0.15)',
            backdropFilter: 'blur(15px)',
            border: '1px solid rgba(255,255,255,0.2)',
            borderRadius: '20px',
            padding: '40px 30px',
            textAlign: 'center',
            transition: 'all 0.3s ease',
            boxShadow: '0 8px 32px rgba(0,0,0,0.1)'
          }}>
            <div className="simple-landing-feature-icon" style={{
              fontSize: '4rem',
              marginBottom: '20px',
              filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.3))'
            }}>🐄</div>
            <h3 style={{
              color: 'white',
              fontSize: '1.5rem',
              fontWeight: 'bold',
              marginBottom: '15px',
              textShadow: '1px 1px 2px rgba(0,0,0,0.5)'
            }}>Smart Livestock Tracking</h3>
            <p style={{
              color: 'rgba(255,255,255,0.9)',
              fontSize: '1.1rem',
              lineHeight: '1.6',
              textShadow: '1px 1px 2px rgba(0,0,0,0.3)'
            }}>Real-time monitoring of cattle, sheep, and goats with RFID technology and AI-powered insights</p>
          </div>

          <div className="simple-landing-feature-card" style={{
            background: 'rgba(255,255,255,0.15)',
            backdropFilter: 'blur(15px)',
            border: '1px solid rgba(255,255,255,0.2)',
            borderRadius: '20px',
            padding: '40px 30px',
            textAlign: 'center',
            transition: 'all 0.3s ease',
            boxShadow: '0 8px 32px rgba(0,0,0,0.1)'
          }}>
            <div className="simple-landing-feature-icon" style={{
              fontSize: '4rem',
              marginBottom: '20px',
              filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.3))'
            }}>🩺</div>
            <h3 style={{
              color: 'white',
              fontSize: '1.5rem',
              fontWeight: 'bold',
              marginBottom: '15px',
              textShadow: '1px 1px 2px rgba(0,0,0,0.5)'
            }}>AI Health Management</h3>
            <p style={{
              color: 'rgba(255,255,255,0.9)',
              fontSize: '1.1rem',
              lineHeight: '1.6',
              textShadow: '1px 1px 2px rgba(0,0,0,0.3)'
            }}>Predictive health analytics to prevent diseases, reduce vet costs, and keep your livestock healthy</p>
          </div>

          <div className="simple-landing-feature-card" style={{
            background: 'rgba(255,255,255,0.15)',
            backdropFilter: 'blur(15px)',
            border: '1px solid rgba(255,255,255,0.2)',
            borderRadius: '20px',
            padding: '40px 30px',
            textAlign: 'center',
            transition: 'all 0.3s ease',
            boxShadow: '0 8px 32px rgba(0,0,0,0.1)'
          }}>
            <div className="simple-landing-feature-icon" style={{
              fontSize: '4rem',
              marginBottom: '20px',
              filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.3))'
            }}>📊</div>
            <h3 style={{
              color: 'white',
              fontSize: '1.5rem',
              fontWeight: 'bold',
              marginBottom: '15px',
              textShadow: '1px 1px 2px rgba(0,0,0,0.5)'
            }}>Advanced Analytics</h3>
            <p style={{
              color: 'rgba(255,255,255,0.9)',
              fontSize: '1.1rem',
              lineHeight: '1.6',
              textShadow: '1px 1px 2px rgba(0,0,0,0.3)'
            }}>Comprehensive insights, financial tracking, and reporting for smarter farm management decisions</p>
          </div>

          <div className="simple-landing-feature-card" style={{
            background: 'rgba(255,255,255,0.15)',
            backdropFilter: 'blur(15px)',
            border: '1px solid rgba(255,255,255,0.2)',
            borderRadius: '20px',
            padding: '40px 30px',
            textAlign: 'center',
            transition: 'all 0.3s ease',
            boxShadow: '0 8px 32px rgba(0,0,0,0.1)'
          }}>
            <div className="simple-landing-feature-icon" style={{
              fontSize: '4rem',
              marginBottom: '20px',
              filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.3))'
            }}>🌾</div>
            <h3 style={{
              color: 'white',
              fontSize: '1.5rem',
              fontWeight: 'bold',
              marginBottom: '15px',
              textShadow: '1px 1px 2px rgba(0,0,0,0.5)'
            }}>Feed Management</h3>
            <p style={{
              color: 'rgba(255,255,255,0.9)',
              fontSize: '1.1rem',
              lineHeight: '1.6',
              textShadow: '1px 1px 2px rgba(0,0,0,0.3)'
            }}>Optimize nutrition, track feed costs, and automate feeding schedules for maximum efficiency</p>
          </div>

          <div className="simple-landing-feature-card" style={{
            background: 'rgba(255,255,255,0.15)',
            backdropFilter: 'blur(15px)',
            border: '1px solid rgba(255,255,255,0.2)',
            borderRadius: '20px',
            padding: '40px 30px',
            textAlign: 'center',
            transition: 'all 0.3s ease',
            boxShadow: '0 8px 32px rgba(0,0,0,0.1)'
          }}>
            <div className="simple-landing-feature-icon" style={{
              fontSize: '4rem',
              marginBottom: '20px',
              filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.3))'
            }}>🛡️</div>
            <h3 style={{
              color: 'white',
              fontSize: '1.5rem',
              fontWeight: 'bold',
              marginBottom: '15px',
              textShadow: '1px 1px 2px rgba(0,0,0,0.5)'
            }}>Security & Safety</h3>
            <p style={{
              color: 'rgba(255,255,255,0.9)',
              fontSize: '1.1rem',
              lineHeight: '1.6',
              textShadow: '1px 1px 2px rgba(0,0,0,0.3)'
            }}>Emergency response system with panic button and theft prevention for farm security</p>
          </div>

          <div className="simple-landing-feature-card" style={{
            background: 'rgba(255,255,255,0.15)',
            backdropFilter: 'blur(15px)',
            border: '1px solid rgba(255,255,255,0.2)',
            borderRadius: '20px',
            padding: '40px 30px',
            textAlign: 'center',
            transition: 'all 0.3s ease',
            boxShadow: '0 8px 32px rgba(0,0,0,0.1)'
          }}>
            <div className="simple-landing-feature-icon" style={{
              fontSize: '4rem',
              marginBottom: '20px',
              filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.3))'
            }}>🏪</div>
            <h3 style={{
              color: 'white',
              fontSize: '1.5rem',
              fontWeight: 'bold',
              marginBottom: '15px',
              textShadow: '1px 1px 2px rgba(0,0,0,0.5)'
            }}>Service Marketplace</h3>
            <p style={{
              color: 'rgba(255,255,255,0.9)',
              fontSize: '1.1rem',
              lineHeight: '1.6',
              textShadow: '1px 1px 2px rgba(0,0,0,0.3)'
            }}>Connect with veterinarians, suppliers, auctioneers, and security services in your area</p>
          </div>
        </div>

        {/* Stats */}
        <div className="simple-landing-stats-grid">
          <div className="simple-landing-stat-item">
            <div className="simple-landing-stat-number">15K+</div>
            <div>South African Farmers</div>
          </div>
          <div className="simple-landing-stat-item">
            <div className="simple-landing-stat-number">750K+</div>
            <div>Livestock Tracked</div>
          </div>
          <div className="simple-landing-stat-item">
            <div className="simple-landing-stat-number">99.9%</div>
            <div>System Uptime</div>
          </div>
          <div className="simple-landing-stat-item">
            <div className="simple-landing-stat-number">40%</div>
            <div>Cost Reduction</div>
          </div>
        </div>

        {/* Pricing */}
        <div className="simple-landing-pricing-grid">
          <div className="simple-landing-pricing-card">
            <h3>Beta Access</h3>
            <div className="simple-landing-pricing-price">FREE</div>
            <p>Perfect for small-scale farmers</p>
            <ul className="simple-landing-pricing-features">
              <li>✅ Up to 50 animals</li>
              <li>✅ Basic health monitoring</li>
              <li>✅ Mobile app access</li>
            </ul>
            <button
              onClick={handleBetaClick}
              className="simple-landing-pricing-button beta"
            >
              START BETA
            </button>
          </div>

          <div className="simple-landing-pricing-card featured">
            <h3>Professional</h3>
            <div className="simple-landing-pricing-price">R299</div>
            <p>For growing commercial farms</p>
            <ul className="simple-landing-pricing-features">
              <li>✅ Up to 500 animals</li>
              <li>✅ AI health analytics</li>
              <li>✅ Financial management</li>
              <li>✅ Priority support</li>
            </ul>
            <button
              onClick={handleLiveClick}
              className="simple-landing-pricing-button live"
            >
              GO LIVE
            </button>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="simple-landing-footer">
        <h3>🌾 AgriIntel</h3>
        <p>Smart Livestock Management for South African Farmers</p>
        <div className="simple-landing-footer-contact">
          <span>📞 +27 11 123 4567</span>
          <span>📧 <EMAIL></span>
          <span>📍 Cape Town, South Africa</span>
        </div>
        <p className="simple-landing-footer-copyright">
          © 2024 AgriIntel. All rights reserved. Made with ❤️ by May and Caiphus for South African farmers.
        </p>
      </footer>
    </div>
  );
};

export default SimpleLanding;
