/* Final Landing Page Styles */

.final-landing {
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Background Layers */
.final-landing-bg-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 1;
}

.final-landing-gradient-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
    rgba(46, 125, 50, 0.85) 0%,
    rgba(76, 175, 80, 0.75) 25%,
    rgba(139, 195, 74, 0.65) 50%,
    rgba(102, 187, 106, 0.75) 75%,
    rgba(67, 160, 71, 0.85) 100%
  );
  z-index: 2;
}

.final-landing-pattern-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255,255,255,0.05) 0%, transparent 50%);
  z-index: 3;
}

/* Navigation */
.final-landing-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  padding: 1.5rem 3rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(0, 0, 0, 0.1);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 100;
}

.final-landing-brand {
  font-size: 2.8rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 30px rgba(102, 126, 234, 0.5);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.final-landing-brand-beta {
  font-size: 0.4em;
  background: rgba(102, 126, 234, 0.2);
  padding: 0.2em 0.5em;
  border-radius: 20px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
}

.final-landing-nav-buttons {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.final-landing-btn-primary {
  padding: 0.8rem 1.8rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4);
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.final-landing-btn-primary:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.6);
}

.final-landing-btn-secondary {
  padding: 0.8rem 1.8rem;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.final-landing-btn-secondary:hover {
  transform: translateY(-3px) scale(1.05);
  background: rgba(255, 255, 255, 0.2);
}

/* Hero Section */
.final-landing-hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 8rem 2rem 4rem;
  position: relative;
  z-index: 10;
}

.final-landing-hero-content {
  max-width: 900px;
  animation: fadeInUp 1s ease-out;
}

.final-landing-badge {
  background: rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  padding: 0.8rem 2rem;
  border-radius: 50px;
  display: inline-block;
  margin-bottom: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  font-size: 0.9rem;
  font-weight: 600;
  letter-spacing: 1px;
  color: white;
}

.final-landing-hero-title {
  font-size: clamp(3rem, 8vw, 6rem);
  margin-bottom: 1.5rem;
  font-weight: 900;
  line-height: 1.1;
  color: white;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.final-landing-hero-subtitle {
  font-size: clamp(1.2rem, 2.5vw, 1.6rem);
  margin-bottom: 3rem;
  opacity: 0.95;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.95);
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.final-landing-hero-buttons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 4rem;
}

.final-landing-hero-btn-primary {
  padding: 1.2rem 2.5rem;
  background: rgba(255, 255, 255, 0.9);
  color: #2E7D32;
  border: none;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  text-decoration: none;
  display: inline-block;
}

.final-landing-hero-btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3);
}

.final-landing-hero-btn-secondary {
  padding: 1.2rem 2.5rem;
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.6);
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.final-landing-hero-btn-secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: white;
  transform: translateY(-3px);
}

/* Stats Section */
.final-landing-stats {
  display: flex;
  gap: 3rem;
  justify-content: center;
  flex-wrap: wrap;
}

.final-landing-stat {
  text-align: center;
}

.final-landing-stat-number {
  font-size: 2.5rem;
  font-weight: 900;
  color: white;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.final-landing-stat-label {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .final-landing-nav {
    padding: 1rem 1.5rem;
    flex-direction: column;
    gap: 1rem;
  }
  
  .final-landing-brand {
    font-size: 2rem;
  }
  
  .final-landing-hero {
    padding: 6rem 1rem 2rem;
  }
  
  .final-landing-hero-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .final-landing-stats {
    gap: 2rem;
  }

  .section-full {
    padding: 4rem 2rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .image-gallery-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

/* Additional styles for inline style replacements */
.section-full {
  width: 100%;
  padding: 6rem 4rem;
  background: rgba(0, 0, 0, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.section-full-content {
  max-width: 1600px;
  margin: 0 auto;
  text-align: center;
  margin-bottom: 4rem;
}

.section-full h2 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  margin-bottom: 1.5rem;
  font-weight: 800;
  color: white;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.section-full p {
  font-size: 1.2rem;
  opacity: 0.9;
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  max-width: 1600px;
  margin: 0 auto;
}

.feature-card {
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(30px);
  backdrop-filter: blur(30px);
  padding: 2.5rem;
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.feature-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 60px rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.15);
}

.feature-card .icon {
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.3));
}

.feature-card h3 {
  font-size: 1.4rem;
  margin-bottom: 1rem;
  font-weight: 700;
  color: white;
}

.feature-card p {
  opacity: 0.9;
  line-height: 1.6;
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
}

.livestock-showcase {
  background: rgba(0, 0, 0, 0.3);
  padding: 4rem 2rem;
  position: relative;
  overflow: hidden;
}

.livestock-showcase-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0.3;
  transition: all 1s ease-in-out;
  z-index: 0;
}

.livestock-showcase-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(27, 94, 32, 0.8) 0%, rgba(46, 125, 50, 0.6) 100%);
  z-index: 1;
}

.livestock-showcase-content {
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
  text-align: center;
}

.livestock-showcase h2 {
  font-size: 3rem;
  margin-bottom: 2rem;
  background: linear-gradient(45deg, #FFD700, #FFA500);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.livestock-showcase p {
  font-size: 1.5rem;
  margin-bottom: 3rem;
  opacity: 0.95;
  max-width: 800px;
  margin: 0 auto 3rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
}

.image-gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.gallery-item {
  position: relative;
  height: 250px;
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.gallery-item.active {
  border: 3px solid #FFD700;
  box-shadow: 0 8px 32px rgba(255, 215, 0, 0.4);
}

.gallery-item:hover {
  transform: scale(1.05);
  box-shadow: 0 12px 40px rgba(255, 215, 0, 0.3);
}

.gallery-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.gallery-item-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 1rem;
  text-align: center;
}

.gallery-item-title {
  font-weight: bold;
  font-size: 1.1rem;
}
