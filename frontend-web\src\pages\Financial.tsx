import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useFinancialData } from '../hooks/useFinancialData';
import { useAuth } from '../contexts/AuthContext';
import Loading from '../components/Loading';
import BetaFinancialModule from '../components/beta/BetaFinancialModule';
import '../styles/theme/global-theme.css';
import styles from '../styles/components/financial.module.css';

const Financial: React.FC = () => {
  const { user } = useAuth();
  const { stats, budgets, transactions, loading, error } = useFinancialData();
  const [timeframe, setTimeframe] = useState('monthly');

  // Check if user is BETA user - using username for beta identification
  const isBetaUser = user?.username === 'Demo' || user?.username === 'demo';

  // If BETA user, render BETA Financial Module
  if (isBetaUser) {
    return <BetaFinancialModule />;
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  if (loading) {
    return <Loading />;
  }

  if (error) {
    return <div className="text-red-500">Error loading financial data: {error}</div>;
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className={styles.container}
    >
      <div className={styles.header}>
        <h1 className={styles.title}>Financial Management</h1>
        <div className={styles.controls}>
          <select
            className={styles.select}
            value={timeframe}
            onChange={(e) => setTimeframe(e.target.value)}
            title="Select timeframe for financial data"
            aria-label="Select timeframe for financial data"
          >
            <option value="weekly">Weekly</option>
            <option value="monthly">Monthly</option>
            <option value="quarterly">Quarterly</option>
            <option value="yearly">Yearly</option>
          </select>
          <button
            type="button"
            className={styles.buttonPrimary}
            aria-label="Generate financial report"
          >
            Generate Report
          </button>
        </div>
      </div>
      
      {/* Financial Summary */}
      <div className={styles.statsGrid}>
        <div className={styles.statCard}>
          <h3 className={styles.statLabel}>Total Revenue</h3>
          <p className={styles.statValuePositive}>{formatCurrency(stats.totalRevenue)}</p>
        </div>
        <div className={styles.statCard}>
          <h3 className={styles.statLabel}>Total Expenses</h3>
          <p className={styles.statValueNegative}>{formatCurrency(stats.totalExpenses)}</p>
        </div>
        <div className={styles.statCard}>
          <h3 className={styles.statLabel}>Net Profit</h3>
          <p className={styles.statValueNeutral}>{formatCurrency(stats.netProfit)}</p>
        </div>
        <div className={styles.statCard}>
          <h3 className={styles.statLabel}>Growth Rate</h3>
          <p className={styles.statValueNeutral}>{stats.growthRate}%</p>
        </div>
      </div>

      <div className={styles.contentGrid}>
        {/* Recent Transactions */}
        <div className={styles.card}>
          <div className={styles.cardHeader}>
            <h2 className={styles.cardTitle}>Recent Transactions</h2>
            <button type="button" className={styles.cardAction}>View All</button>
          </div>
          <div className={styles.cardContent}>
            <table className={styles.table}>
              <thead>
                <tr>
                  <th className={styles.tableHeader}>Date</th>
                  <th className={styles.tableHeader}>Description</th>
                  <th className={styles.tableHeader}>Amount</th>
                </tr>
              </thead>
              <tbody>
                {transactions.map(transaction => (
                  <tr key={transaction.id} className={styles.tableRow}>
                    <td className={styles.tableCell}>{new Date(transaction.date).toLocaleDateString()}</td>
                    <td className={styles.tableCell}>{transaction.description}</td>
                    <td className={transaction.type === 'income' ? styles.tableCellPositive : styles.tableCellNegative}>
                      {formatCurrency(transaction.amount)}
                    </td>
                  </tr>
                ))}
                {/* Add some placeholder rows if we have few transactions */}
                {transactions.length === 0 && (
                  <tr>
                    <td colSpan={3} className={styles.emptyState}>No recent transactions</td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Budget Overview */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-4 border-b flex justify-between items-center">
            <h2 className="text-lg font-semibold">Budget Overview</h2>
            <button className="text-blue-500 hover:text-blue-700">Manage Budgets</button>
          </div>
          <div className="p-4">
            {budgets.map((budget) => (
              <div key={budget.category} className="mb-4 last:mb-0">
                <div className="flex justify-between mb-2">
                  <span className="capitalize">{budget.category}</span>
                  <span>{formatCurrency(budget.spent)} / {formatCurrency(budget.allocated)}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full"
                    style={{ width: `${budget.percentage}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Financial Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white p-6 rounded-lg shadow text-center">
          <div className="text-3xl text-blue-500 mb-2">📊</div>
          <h3 className="font-semibold mb-2">Financial Reports</h3>
          <p className="text-gray-600 text-sm mb-4">Generate detailed financial reports and statements</p>
          <button className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors w-full">
            Generate Reports
          </button>
        </div>
        <div className="bg-white p-6 rounded-lg shadow text-center">
          <div className="text-3xl text-green-500 mb-2">💰</div>
          <h3 className="font-semibold mb-2">Record Transaction</h3>
          <p className="text-gray-600 text-sm mb-4">Add new income or expense transactions</p>
          <button className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors w-full">
            New Transaction
          </button>
        </div>
        <div className="bg-white p-6 rounded-lg shadow text-center">
          <div className="text-3xl text-purple-500 mb-2">📝</div>
          <h3 className="font-semibold mb-2">Budget Planning</h3>
          <p className="text-gray-600 text-sm mb-4">Create and manage budgets for different categories</p>
          <button className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors w-full">
            Manage Budgets
          </button>
        </div>
      </div>
    </motion.div>
  );
};

export default Financial;
